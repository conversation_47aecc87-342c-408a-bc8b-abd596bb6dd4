package com.heytap.media.iqiyi.standard.spi;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.heytap.longvideo.client.media.entity.MisAlbumOrg;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.media.common.enums.SourceEnum;
import com.heytap.media.common.message.AlbumHotInfo;
import com.heytap.media.common.thirdparty.spi.standard.AlbumStandardSPI;
import com.heytap.media.iqiyi.constant.IqiyiConstant;
import com.heytap.media.iqiyi.util.IqiyiUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 爱奇艺 媒资标准化SPI实现
 * @Author: 80398885WT
 * @Date: 2025/07/17
 */
@Data
@Slf4j
@Service
@AllArgsConstructor
public class IqiyiAlbumStandardSPI implements AlbumStandardSPI {

    private IqiyiUtil iqiyiUtil;

    private static final String YEAR_REGEX = "\\d{4}";

    /**
     * 特殊字段
     */
    private final Set<String> specialField = new HashSet<>(Arrays.asList("year", "tags", "actor", "director", "duration", "featureType"));

    private final Set<String> needToFilter = new HashSet<>(Arrays.asList("其他", "其它", "其祂", "未知"));

    private final Map<String, BiConsumer<StandardAlbum, Object>> specialFieldMap = new HashMap<String, BiConsumer<StandardAlbum, Object>>() {{
        put("year", (standardAlbum, obj) -> {
            String yearStr = String.valueOf(obj);
            if (StringUtils.isNotEmpty(yearStr)) {
                Pattern pattern = Pattern.compile(YEAR_REGEX);
                Matcher matcher = pattern.matcher(yearStr);
                if (matcher.find()) {
                    standardAlbum.setYear(Integer.parseInt(matcher.group()));
                }
            }
        });

        put("duration", (standardAlbum, obj) -> {
            if (Objects.isNull(obj)) {
                return;
            }

            if (Objects.equals(String.valueOf(obj), "-1")) {
                standardAlbum.setDuration(0);
                return;
            }
            standardAlbum.setDuration(Integer.parseInt(String.valueOf(obj)));
        });

        put("actor", (standardAlbum, obj) -> {
            if (Objects.isNull(obj)) {
                return;
            }

            String actorStr = String.valueOf(obj);
            if (StringUtils.isEmpty(actorStr)) {
                return;
            }

            Set<String> actorSet = Sets.newLinkedHashSet(Lists.newArrayList(actorStr.split(",")));
            actorSet.removeIf(needToFilter::contains);
            standardAlbum.setActor(StringUtils.join(actorSet, "|"));
        });

        put("director", (standardAlbum, obj) -> {
            if (Objects.isNull(obj)) {
                return;
            }

            String directorStr = String.valueOf(obj);
            if (StringUtils.isEmpty(directorStr)) {
                return;
            }

            Set<String> directorSet = Sets.newLinkedHashSet(Lists.newArrayList(directorStr.split(",")));
            directorSet.removeIf(needToFilter::contains);
            standardAlbum.setDirector(StringUtils.join(directorSet, "|"));
        });

        put("tags", (standardAlbum, obj) -> {
            if (Objects.isNull(obj)) {
                return;
            }

            String tagsStr = String.valueOf(obj);
            if (StringUtils.isEmpty(tagsStr)) {
                return;
            }

            Set<String> tagSet = Sets.newLinkedHashSet(Lists.newArrayList(tagsStr.split(",")));
            tagSet.removeIf(needToFilter::contains);
            standardAlbum.setTags(StringUtils.join(tagSet, "|"));
        });

        put("featureType", (standardAlbum, obj) -> {
            if (Objects.isNull(obj)) {
                return;
            }

            String featureTypeStr = String.valueOf(obj);
            if (StringUtils.isEmpty(featureTypeStr)) {
                return;
            }

            if (Objects.equals(featureTypeStr, "1")) {
                standardAlbum.setFeatureType(1);
            } else if (Objects.equals(featureTypeStr, "3")) {
                standardAlbum.setFeatureType(2);
            }
        });
    }};

    // 字段后处理逻辑
    @Override
    public void postProcessFields(MisAlbumOrg misAlbumOrg, StandardAlbum standardAlbum) {
    }

    @Override
    public AlbumHotInfo getHotInfo(StandardAlbum standardAlbum) {
        return AlbumStandardSPI.super.getHotInfo(standardAlbum);
    }

    @Override
    public List<String> getHandlerProgram() {
        return AlbumStandardSPI.super.getHandlerProgram();
    }

    @Override
    public SourceEnum getCpSource() {
        return SourceEnum.IQIYI_MOBILE;
    }

    @Override
    public boolean isSpecialMappingField(String filedName) {
        return specialField.contains(filedName);
    }

    @Override
    public void processSpecialMappingField(StandardAlbum standardAlbum, Object obj, Field field) {
        specialFieldMap.get(field.getName()).accept(standardAlbum, obj);
    }

    // 对特定字段进行处理
    @Override
    public void postProcessMappingFields(StandardAlbum standardAlbum) {
        standardAlbum.setStatus(1);
        standardAlbum.setSource(SourceEnum.IQIYI_MOBILE.getSpiSource());
        standardAlbum.setSourceType(0);
        standardAlbum.setCopyright(1);
        standardAlbum.setCopyrightCode("iqiyi");
        standardAlbum.setProcessStatus(1);
        standardAlbum.setCreateTime(new Date());
        standardAlbum.setUpdateTime(new Date());
        standardAlbum.setTitle(iqiyiUtil.matchTitle(standardAlbum.getTitle()));
        standardAlbum.setCategory(IqiyiConstant.ProgramTypeEnum.getCategoryByChannelId(standardAlbum.getCategory()));
        standardAlbum.setProgramType(IqiyiConstant.ProgramTypeEnum.getProgramTypeByChannelId(standardAlbum.getProgramType()));
        standardAlbum.setSubProgramType(standardAlbum.getProgramType());
        standardAlbum.setManagerStatus(0);
        standardAlbum.setUnit(IqiyiUtil.getUnit(standardAlbum.getProgramType()));
    }
}