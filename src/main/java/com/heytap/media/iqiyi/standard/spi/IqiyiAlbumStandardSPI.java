package com.heytap.media.iqiyi.standard.spi;

import com.heytap.longvideo.client.media.entity.MisAlbumOrg;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.media.common.enums.SourceEnum;
import com.heytap.media.common.message.AlbumHotInfo;
import com.heytap.media.common.thirdparty.spi.standard.AlbumStandardSPI;
import com.heytap.media.iqiyi.constant.IqiyiConstant;
import com.heytap.media.iqiyi.util.IqiyiUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 爱奇艺 媒资标准化SPI实现
 * @Author: 80398885WT
 * @Date: 2025/07/17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IqiyiAlbumStandardSPI implements AlbumStandardSPI {

    private final IqiyiUtil iqiyiUtil;

    // 常量定义
    private static final Pattern YEAR_PATTERN = Pattern.compile("\\d{4}");
    private static final String INVALID_DURATION = "-1";
    private static final String COPYRIGHT_CODE = "iqiyi";
    private static final String FIELD_SEPARATOR = ",";
    private static final String OUTPUT_SEPARATOR = "|";

    // 特征类型映射
    private static final Map<String, Integer> FEATURE_TYPE_MAPPING = Map.of(
        "1", 1,
        "3", 2
    );

    // 状态常量
    private static final int ACTIVE_STATUS = 1;
    private static final int SOURCE_TYPE = 0;
    private static final int COPYRIGHT_STATUS = 1;
    private static final int PROCESS_STATUS = 1;
    private static final int MANAGER_STATUS = 0;
    private static final int DEFAULT_DURATION = 0;

    /**
     * 特殊字段集合
     */
    private static final Set<String> SPECIAL_FIELDS = Set.of(
        "year", "tags", "actor", "director", "duration", "featureType"
    );

    /**
     * 需要过滤的无效值
     */
    private static final Set<String> INVALID_VALUES = Set.of(
        "其他", "其它", "其祂", "未知"
    );

    /**
     * 特殊字段处理器映射
     */
    private Map<String, BiConsumer<StandardAlbum, Object>> fieldProcessors;

    /**
     * 初始化字段处理器
     */
    @PostConstruct
    public void initFieldProcessors() {
        fieldProcessors = new HashMap<>();
        fieldProcessors.put("year", this::processYearField);
        fieldProcessors.put("duration", this::processDurationField);
        fieldProcessors.put("actor", (album, obj) -> processStringListField(album::setActor, obj));
        fieldProcessors.put("director", (album, obj) -> processStringListField(album::setDirector, obj));
        fieldProcessors.put("tags", (album, obj) -> processStringListField(album::setTags, obj));
        fieldProcessors.put("featureType", this::processFeatureTypeField);
    }

    /**
     * 处理年份字段
     */
    private void processYearField(StandardAlbum standardAlbum, Object obj) {
        if (Objects.isNull(obj)) {
            return;
        }

        String yearStr = String.valueOf(obj).trim();
        if (StringUtils.isEmpty(yearStr)) {
            return;
        }

        try {
            var matcher = YEAR_PATTERN.matcher(yearStr);
            if (matcher.find()) {
                standardAlbum.setYear(Integer.parseInt(matcher.group()));
            }
        } catch (NumberFormatException e) {
            log.warn("解析年份失败: {}", yearStr, e);
        }
    }

    /**
     * 处理时长字段
     */
    private void processDurationField(StandardAlbum standardAlbum, Object obj) {
        if (Objects.isNull(obj)) {
            return;
        }

        String durationStr = String.valueOf(obj).trim();
        if (StringUtils.isEmpty(durationStr)) {
            return;
        }

        try {
            if (INVALID_DURATION.equals(durationStr)) {
                standardAlbum.setDuration(DEFAULT_DURATION);
            } else {
                standardAlbum.setDuration(Integer.parseInt(durationStr));
            }
        } catch (NumberFormatException e) {
            log.warn("解析时长失败: {}", durationStr, e);
            standardAlbum.setDuration(DEFAULT_DURATION);
        }
    }

    /**
     * 处理字符串列表字段（演员、导演、标签）
     */
    private void processStringListField(java.util.function.Consumer<String> setter, Object obj) {
        if (Objects.isNull(obj)) {
            return;
        }

        String inputStr = String.valueOf(obj).trim();
        if (StringUtils.isEmpty(inputStr)) {
            return;
        }

        try {
            String processedValue = Arrays.stream(inputStr.split(FIELD_SEPARATOR))
                    .map(String::trim)
                    .filter(StringUtils::isNotEmpty)
                    .filter(value -> !INVALID_VALUES.contains(value))
                    .distinct()
                    .collect(Collectors.joining(OUTPUT_SEPARATOR));

            if (StringUtils.isNotEmpty(processedValue)) {
                setter.accept(processedValue);
            }
        } catch (Exception e) {
            log.warn("处理字符串列表字段失败: {}", inputStr, e);
        }
    }

    /**
     * 处理特征类型字段
     */
    private void processFeatureTypeField(StandardAlbum standardAlbum, Object obj) {
        if (Objects.isNull(obj)) {
            return;
        }

        String featureTypeStr = String.valueOf(obj).trim();
        if (StringUtils.isEmpty(featureTypeStr)) {
            return;
        }

        Integer mappedValue = FEATURE_TYPE_MAPPING.get(featureTypeStr);
        if (Objects.nonNull(mappedValue)) {
            standardAlbum.setFeatureType(mappedValue);
        } else {
            log.debug("未知的特征类型: {}", featureTypeStr);
        }
    }

    // 字段后处理逻辑
    @Override
    public void postProcessFields(MisAlbumOrg misAlbumOrg, StandardAlbum standardAlbum) {
    }

    @Override
    public AlbumHotInfo getHotInfo(StandardAlbum standardAlbum) {
        return AlbumStandardSPI.super.getHotInfo(standardAlbum);
    }

    @Override
    public List<String> getHandlerProgram() {
        return AlbumStandardSPI.super.getHandlerProgram();
    }

    @Override
    public SourceEnum getCpSource() {
        return SourceEnum.IQIYI_MOBILE;
    }

    @Override
    public boolean isSpecialMappingField(String fieldName) {
        return SPECIAL_FIELDS.contains(fieldName);
    }

    @Override
    public void processSpecialMappingField(StandardAlbum standardAlbum, Object obj, Field field) {
        try {
            BiConsumer<StandardAlbum, Object> processor = fieldProcessors.get(field.getName());
            if (Objects.nonNull(processor)) {
                processor.accept(standardAlbum, obj);
            } else {
                log.warn("未找到字段处理器: {}", field.getName());
            }
        } catch (Exception e) {
            log.error("处理特殊字段时发生异常: {}", field.getName(), e);
        }
    }

    /**
     * 对特定字段进行后处理
     */
    @Override
    public void postProcessMappingFields(StandardAlbum standardAlbum) {
        try {
            // 设置基础状态字段
            setBasicStatusFields(standardAlbum);

            // 设置时间字段
            Date currentTime = new Date();
            standardAlbum.setCreateTime(currentTime);
            standardAlbum.setUpdateTime(currentTime);

            // 处理业务字段
            processBusinessFields(standardAlbum);

        } catch (Exception e) {
            log.error("后处理映射字段时发生异常", e);
        }
    }

    /**
     * 设置基础状态字段
     */
    private void setBasicStatusFields(StandardAlbum standardAlbum) {
        standardAlbum.setStatus(ACTIVE_STATUS);
        standardAlbum.setSource(SourceEnum.IQIYI_MOBILE.getSpiSource());
        standardAlbum.setSourceType(SOURCE_TYPE);
        standardAlbum.setCopyright(COPYRIGHT_STATUS);
        standardAlbum.setCopyrightCode(COPYRIGHT_CODE);
        standardAlbum.setProcessStatus(PROCESS_STATUS);
        standardAlbum.setManagerStatus(MANAGER_STATUS);
    }

    /**
     * 处理业务相关字段
     */
    private void processBusinessFields(StandardAlbum standardAlbum) {
        // 处理标题
        if (StringUtils.isNotEmpty(standardAlbum.getTitle())) {
            standardAlbum.setTitle(iqiyiUtil.matchTitle(standardAlbum.getTitle()));
        }

        // 处理分类和节目类型
        if (Objects.nonNull(standardAlbum.getCategory())) {
            standardAlbum.setCategory(IqiyiConstant.ProgramTypeEnum.getCategoryByChannelId(standardAlbum.getCategory()));
        }

        if (Objects.nonNull(standardAlbum.getProgramType())) {
            Integer programType = IqiyiConstant.ProgramTypeEnum.getProgramTypeByChannelId(standardAlbum.getProgramType());
            standardAlbum.setProgramType(programType);
            standardAlbum.setSubProgramType(programType);
            standardAlbum.setUnit(IqiyiUtil.getUnit(programType));
        }
    }
}