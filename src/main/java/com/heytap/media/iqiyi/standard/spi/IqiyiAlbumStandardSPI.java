package com.heytap.media.iqiyi.standard.spi;

import com.heytap.longvideo.client.media.entity.MisAlbumOrg;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.media.common.enums.SourceEnum;
import com.heytap.media.common.message.AlbumHotInfo;
import com.heytap.media.common.thirdparty.spi.standard.AlbumStandardSPI;
import com.heytap.media.iqiyi.constant.IqiyiConstant;
import com.heytap.media.iqiyi.util.IqiyiUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 爱奇艺 媒资标准化SPI实现
 * @Author: 80398885WT
 * @Date: 2025/07/17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IqiyiAlbumStandardSPI implements AlbumStandardSPI {

    private final IqiyiUtil iqiyiUtil;

    // 常量定义
    private static final Pattern YEAR_PATTERN = Pattern.compile("\\d{4}");
    private static final String INVALID_DURATION = "-1";
    private static final String COPYRIGHT_CODE = "iqiyi";
    private static final String FIELD_SEPARATOR = ",";
    private static final String OUTPUT_SEPARATOR = "|";

    // 特征类型映射 (Java 8 兼容)
    private static final Map<String, Integer> FEATURE_TYPE_MAPPING;
    static {
        Map<String, Integer> mapping = new HashMap<>();
        mapping.put("1", 1);
        mapping.put("3", 2);
        FEATURE_TYPE_MAPPING = Collections.unmodifiableMap(mapping);
    }

    // 状态常量
    private static final int ACTIVE_STATUS = 1;
    private static final int SOURCE_TYPE = 0;
    private static final int COPYRIGHT_STATUS = 1;
    private static final int PROCESS_STATUS = 1;
    private static final int MANAGER_STATUS = 0;
    private static final int DEFAULT_DURATION = 0;

    /**
     * 特殊字段集合 (Java 8 兼容)
     */
    private static final Set<String> SPECIAL_FIELDS;
    static {
        Set<String> fields = new HashSet<>();
        fields.add("year");
        fields.add("tags");
        fields.add("actor");
        fields.add("director");
        fields.add("duration");
        fields.add("featureType");
        SPECIAL_FIELDS = Collections.unmodifiableSet(fields);
    }

    /**
     * 需要过滤的无效值 (Java 8 兼容)
     */
    private static final Set<String> INVALID_VALUES;
    static {
        Set<String> values = new HashSet<>();
        values.add("其他");
        values.add("其它");
        values.add("其祂");
        values.add("未知");
        INVALID_VALUES = Collections.unmodifiableSet(values);
    }

    /**
     * 特殊字段处理器映射
     */
    private Map<String, BiConsumer<StandardAlbum, Object>> fieldProcessors;

    /**
     * 安全地解析整数，Java 8 Optional 风格
     */
    private Optional<Integer> parseIntegerSafely(String str) {
        return Optional.ofNullable(str)
                .map(String::trim)
                .filter(StringUtils::isNotEmpty)
                .map(s -> {
                    try {
                        return Integer.parseInt(s);
                    } catch (NumberFormatException e) {
                        log.warn("解析整数失败: {}", s, e);
                        return null;
                    }
                });
    }

    /**
     * 安全地提取年份，Java 8 Stream 风格
     */
    private Optional<Integer> extractYearSafely(String str) {
        return Optional.ofNullable(str)
                .map(String::trim)
                .filter(StringUtils::isNotEmpty)
                .map(YEAR_PATTERN::matcher)
                .filter(Matcher::find)
                .map(Matcher::group)
                .flatMap(this::parseIntegerSafely);
    }

    /**
     * 初始化字段处理器 (Java 8 兼容)
     */
    @PostConstruct
    public void initFieldProcessors() {
        fieldProcessors = new HashMap<>();
        fieldProcessors.put("year", this::processYearField);
        fieldProcessors.put("duration", this::processDurationField);
        fieldProcessors.put("actor", this::processActorField);
        fieldProcessors.put("director", this::processDirectorField);
        fieldProcessors.put("tags", this::processTagsField);
        fieldProcessors.put("featureType", this::processFeatureTypeField);
    }

    /**
     * 处理演员字段
     */
    private void processActorField(StandardAlbum standardAlbum, Object obj) {
        processStringListField(standardAlbum::setActor, obj);
    }

    /**
     * 处理导演字段
     */
    private void processDirectorField(StandardAlbum standardAlbum, Object obj) {
        processStringListField(standardAlbum::setDirector, obj);
    }

    /**
     * 处理标签字段
     */
    private void processTagsField(StandardAlbum standardAlbum, Object obj) {
        processStringListField(standardAlbum::setTags, obj);
    }

    /**
     * 处理年份字段 (Java 8 Optional 风格)
     */
    private void processYearField(StandardAlbum standardAlbum, Object obj) {
        extractYearSafely(String.valueOf(obj))
                .ifPresent(standardAlbum::setYear);
    }

    /**
     * 处理时长字段 (Java 8 Optional 风格)
     */
    private void processDurationField(StandardAlbum standardAlbum, Object obj) {
        Optional.ofNullable(obj)
                .map(Object::toString)
                .map(String::trim)
                .filter(StringUtils::isNotEmpty)
                .ifPresent(durationStr -> {
                    if (INVALID_DURATION.equals(durationStr)) {
                        standardAlbum.setDuration(DEFAULT_DURATION);
                    } else {
                        parseIntegerSafely(durationStr)
                                .ifPresent(standardAlbum::setDuration);

                        // 如果解析失败，设置默认值
                        if (standardAlbum.getDuration() == null) {
                            standardAlbum.setDuration(DEFAULT_DURATION);
                        }
                    }
                });
    }

    /**
     * 处理字符串列表字段（演员、导演、标签）
     * Java 8 兼容版本
     */
    private void processStringListField(Consumer<String> setter, Object obj) {
        if (Objects.isNull(obj)) {
            return;
        }

        String inputStr = String.valueOf(obj).trim();
        if (StringUtils.isEmpty(inputStr)) {
            return;
        }

        try {
            // 使用 LinkedHashSet 保持顺序并去重
            Set<String> processedSet = Arrays.stream(inputStr.split(FIELD_SEPARATOR))
                    .map(String::trim)
                    .filter(StringUtils::isNotEmpty)
                    .filter(value -> !INVALID_VALUES.contains(value))
                    .collect(Collectors.toCollection(LinkedHashSet::new));

            if (!processedSet.isEmpty()) {
                // Java 8 Stream 方式连接字符串
                String processedValue = processedSet.stream()
                        .collect(Collectors.joining(OUTPUT_SEPARATOR));
                setter.accept(processedValue);
            }
        } catch (Exception e) {
            log.warn("处理字符串列表字段失败: {}", inputStr, e);
        }
    }

    /**
     * 处理特征类型字段 (使用 Java 8 Optional)
     */
    private void processFeatureTypeField(StandardAlbum standardAlbum, Object obj) {
        Optional.ofNullable(obj)
                .map(Object::toString)
                .map(String::trim)
                .filter(StringUtils::isNotEmpty)
                .map(FEATURE_TYPE_MAPPING::get)
                .ifPresent(mappedValue -> {
                    standardAlbum.setFeatureType(mappedValue);
                    log.debug("设置特征类型: {} -> {}", obj, mappedValue);
                });

        // 如果没有找到映射值，记录调试日志
        if (Objects.nonNull(obj) && StringUtils.isNotEmpty(String.valueOf(obj).trim())
                && !FEATURE_TYPE_MAPPING.containsKey(String.valueOf(obj).trim())) {
            log.debug("未知的特征类型: {}", obj);
        }
    }

    // 字段后处理逻辑
    @Override
    public void postProcessFields(MisAlbumOrg misAlbumOrg, StandardAlbum standardAlbum) {
    }

    @Override
    public AlbumHotInfo getHotInfo(StandardAlbum standardAlbum) {
        return AlbumStandardSPI.super.getHotInfo(standardAlbum);
    }

    @Override
    public List<String> getHandlerProgram() {
        return AlbumStandardSPI.super.getHandlerProgram();
    }

    @Override
    public SourceEnum getCpSource() {
        return SourceEnum.IQIYI_MOBILE;
    }

    @Override
    public boolean isSpecialMappingField(String fieldName) {
        return SPECIAL_FIELDS.contains(fieldName);
    }

    @Override
    public void processSpecialMappingField(StandardAlbum standardAlbum, Object obj, Field field) {
        try {
            BiConsumer<StandardAlbum, Object> processor = fieldProcessors.get(field.getName());
            if (Objects.nonNull(processor)) {
                processor.accept(standardAlbum, obj);
            } else {
                log.warn("未找到字段处理器: {}", field.getName());
            }
        } catch (Exception e) {
            log.error("处理特殊字段时发生异常: {}", field.getName(), e);
        }
    }

    /**
     * 对特定字段进行后处理
     */
    @Override
    public void postProcessMappingFields(StandardAlbum standardAlbum) {
        try {
            // 设置基础状态字段
            setBasicStatusFields(standardAlbum);

            // 设置时间字段
            Date currentTime = new Date();
            standardAlbum.setCreateTime(currentTime);
            standardAlbum.setUpdateTime(currentTime);

            // 处理业务字段
            processBusinessFields(standardAlbum);

        } catch (Exception e) {
            log.error("后处理映射字段时发生异常", e);
        }
    }

    /**
     * 设置基础状态字段
     */
    private void setBasicStatusFields(StandardAlbum standardAlbum) {
        standardAlbum.setStatus(ACTIVE_STATUS);
        standardAlbum.setSource(SourceEnum.IQIYI_MOBILE.getSpiSource());
        standardAlbum.setSourceType(SOURCE_TYPE);
        standardAlbum.setCopyright(COPYRIGHT_STATUS);
        standardAlbum.setCopyrightCode(COPYRIGHT_CODE);
        standardAlbum.setProcessStatus(PROCESS_STATUS);
        standardAlbum.setManagerStatus(MANAGER_STATUS);
    }

    /**
     * 处理业务相关字段 (Java 8 Optional 风格)
     */
    private void processBusinessFields(StandardAlbum standardAlbum) {
        // 处理标题 - 使用 Optional 链式调用
        Optional.ofNullable(standardAlbum.getTitle())
                .filter(StringUtils::isNotEmpty)
                .map(iqiyiUtil::matchTitle)
                .ifPresent(standardAlbum::setTitle);

        // 处理分类
        Optional.ofNullable(standardAlbum.getCategory())
                .map(IqiyiConstant.ProgramTypeEnum::getCategoryByChannelId)
                .ifPresent(standardAlbum::setCategory);

        // 处理节目类型 - 链式处理多个相关字段
        Optional.ofNullable(standardAlbum.getProgramType())
                .map(IqiyiConstant.ProgramTypeEnum::getProgramTypeByChannelId)
                .ifPresent(programType -> {
                    standardAlbum.setProgramType(programType);
                    standardAlbum.setSubProgramType(programType);
                    standardAlbum.setUnit(IqiyiUtil.getUnit(programType));
                });
    }
}