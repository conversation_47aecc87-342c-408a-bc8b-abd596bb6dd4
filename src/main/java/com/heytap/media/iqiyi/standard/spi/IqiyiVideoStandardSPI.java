package com.heytap.media.iqiyi.standard.spi;

import com.heytap.longvideo.client.media.entity.MisVideoOrg;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.entity.StandardVideo;
import com.heytap.media.common.enums.SourceEnum;
import com.heytap.media.common.thirdparty.spi.standard.VideoStandardSPI;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;

/**
 * 爱奇艺 视频标准化SPI实现
 * @Author: 80398885WT
 * @Date: 2025/07/17
 */
@Slf4j
@Service
public class IqiyiVideoStandardSPI implements VideoStandardSPI {
    
    @Override
    public void postProcessFields(MisVideoOrg misVideoOrg, StandardVideo standardVideo, StandardAlbum standardAlbum) {
        // TODO: 视频字段后处理逻辑
    }

    @Override
    public SourceEnum getCpSource() {
        return SourceEnum.IQIYI_MOBILE;
    }

    @Override
    public boolean isSpecialMappingField(String s) {
        return false;
    }

    @Override
    public void processSpecialMappingField(StandardVideo standardVideo, Object o, Field field) {
        // TODO: 特殊字段映射处理
    }

    @Override
    public void postProcessMappingFields(StandardVideo standardVideo) {
        // TODO: 视频标准化后处理逻辑
    }
}