package com.heytap.media.iqiyi.util;

import com.heytap.media.common.constant.StandardConstant;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description: 爱奇艺 工具类
 * @Author: 80398885 WT
 * @Date: 2025/07/17
 */
@Data
@Slf4j
@Service
public class IqiyiUtil {

    @HeraclesDynamicConfig(key = "titleRegex", fileName = "regex_config.properties")
    private String titleRegex;

    @HeraclesDynamicConfig(key = "titleRegex1", fileName = "regex_config.properties")
    private String titleRegex1;

    /**
     * 节目名处理，提取标题中的主要部分
     *
     * @param sourceTitle 原始标题
     * @return 处理后的标题
     */
    public String matchTitle(String sourceTitle) {
        Pattern p1 = Pattern.compile(titleRegex), p2 = Pattern.compile(titleRegex1);
        String res = "";
        try {
            Matcher m = p1.matcher(sourceTitle);
            if (m.find()) {
                res = m.group(1);
            } else {
                m = p2.matcher(sourceTitle);
                res = m.find() ? m.group(1) : res;
            }
        } catch (Exception e) {
            log.error("match title error", e);
        }
        return "".equals(res) ? sourceTitle : res;
    }

    public static String fetchFormatTime(LocalDateTime now) {
        // 定义格式器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        // 格式化时间
        return now.format(formatter);// 输出类似：20250721143045
    }

    public static String getSign(Map<String, String> params, String secret) {
        if (Objects.isNull(params) || params.isEmpty() || StringUtils.isEmpty(secret)) {
            return "";
        }

        try {
            // 1. 将参数按ASCII正序排序并拼接
            TreeMap<String, String> sortedParams = new TreeMap<>(params);
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
                if (StringUtils.isNotEmpty(entry.getValue())) {
                    if (sb.length() > 0) {
                        sb.append("&");
                    }
                    sb.append(entry.getKey()).append("=").append(entry.getValue());
                }
            }
            String paramString = sb.toString();

            byte[] bytes;
            SecretKey secretKey = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(secretKey);
            bytes = mac.doFinal(paramString.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(bytes);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("generate sign error", e);
            return "";
        }
    }

    public static int getUnit(String programType) {
        if (StandardConstant.ProgramType.SHOW.equalsIgnoreCase(programType)) {
            return StandardConstant.Unit.QI;
        } else if (StandardConstant.ProgramType.TV.equalsIgnoreCase(programType)) {
            return (StandardConstant.Unit.JI);
        } else if (StandardConstant.ProgramType.MOVIE.equalsIgnoreCase(programType)) {
            return (StandardConstant.Unit.BU);
        } else if (StandardConstant.ProgramType.DOC.equalsIgnoreCase(programType)) {
            return (StandardConstant.Unit.JI);
        } else if (StandardConstant.ProgramType.KIDS.equalsIgnoreCase(programType)) {
            return (StandardConstant.Unit.JI);
        } else if (StandardConstant.ProgramType.COMIC.equalsIgnoreCase(programType)) {
            return (StandardConstant.Unit.JI);
        } else if (StandardConstant.ProgramType.ACG.equalsIgnoreCase(programType)) {
            return StandardConstant.Unit.FAN;
        } else {
            return (StandardConstant.Unit.JI);
        }
    }
}