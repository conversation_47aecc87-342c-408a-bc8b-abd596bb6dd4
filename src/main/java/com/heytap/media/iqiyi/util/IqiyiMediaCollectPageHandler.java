package com.heytap.media.iqiyi.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.heytap.media.common.thirdparty.spi.collect.CollectContext;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @Description: 爱奇艺 处理分页
 * @Author: 80398885 WT
 * @Date: 2025/07/17
 */
@Slf4j
@Service
public class IqiyiMediaCollectPageHandler {

    /**
     * 处理分页信息的代理方法
     *
     * @param context 采集上下文，包含响应数据和分页信息
     */
    @SneakyThrows
    public void handlePageInfoProxy(CollectContext context) {
        // TODO: 根据爱奇艺API响应格式处理失败响应码
        handlePageInfo(context);
    }

    /**
     * 处理翻页信息
     *
     * @param context 采集上下文，包含分页信息
     */
    private static void handlePageInfo(CollectContext context) {
        int pageNo = context.getPageNo();

        if (pageNo == 1) {
            int[] pageSizeAndTotal = fetchPageSizeAndTotal(context.getResponse());
            if (Objects.nonNull(pageSizeAndTotal)) {
                context.setPageSize(pageSizeAndTotal[0]);
                context.setTotal(pageSizeAndTotal[1]);
            }
        }

        int pageSize = context.getPageSize();
        int total = context.getTotal();

        //是否有下一页
        boolean hasMore = pageNo * pageSize < total;
        context.setHasMore(hasMore);
        if (!hasMore) {
            context.setPageNo(1);
            context.setTotal(0);
            context.getBizMap().remove("params");
            context.getBizMap().remove("minId");
            context.getBizMap().remove("categoryId");
            return;
        }

        context.setPageNo(pageNo + 1);

        setMinId(context);
    }

    private static void setMinId(CollectContext context) {
        if (StringUtils.isEmpty(context.getResponse())) {
            return;
        }

        try {
            JsonNode responseJson = JsonUtil.readTree(context.getResponse());
            if (Objects.isNull(responseJson)) {
                return;
            }

            List<JsonNode> list = Lists.newArrayList(responseJson.get("data").elements());
            JsonNode lastJsonNode = list.get(list.size()-1);
            context.getBizMap().put("minId", lastJsonNode.get("id").asText());
        } catch (Exception e) {
            log.error("set minId error", e);
        }
    }

    /**
     * 从API响应中提取分页大小和总数
     *
     * @param response API响应字符串
     * @return 包含分页大小和总数的数组，格式为[pageSize, total]，如果解析失败返回null
     */
    public static int[] fetchPageSizeAndTotal(String response) {
        if (StringUtils.isEmpty(response)) {
            return null;
        }

        JsonNode responseJson = JsonUtil.readTree(response);
        if (Objects.isNull(responseJson) || responseJson.isEmpty()) {
            return null;
        }

        // TODO: 根据爱奇艺API响应格式调整字段名
        // 这里需要根据实际的爱奇艺API响应结构进行调整
        int pageSize = responseJson.get("pagesize") != null ? responseJson.get("pagesize").asInt() : 0;
        int total = responseJson.get("total") != null ? responseJson.get("total").asInt() : 0;
        return new int[]{pageSize, total};
    }
}