package com.heytap.media.iqiyi.collect.spi;

import com.heytap.longvideo.client.media.entity.MisVideoOrg;
import com.heytap.media.common.enums.CollectTypeEnum;
import com.heytap.media.common.enums.SourceEnum;
import com.heytap.media.common.thirdparty.spi.collect.CollectContext;
import com.heytap.media.common.thirdparty.spi.collect.ShortVideoCollectSPI;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 爱奇艺 短视频全量采集SPI实现
 * @Author: 80398885WT
 * @Date: 2025/07/17
 */
@Slf4j
@Service
public class IqiyiShortVideoAllCollectSPI implements ShortVideoCollectSPI {
    
    @Override
    public Map<String, String> getParams(CollectContext collectContext) {
        // TODO: 获取短视频传什么参数？
        Map<String, String> params = new HashMap<>();
        return params;
    }

    @Override
    public SourceEnum getCpSource() {
        return SourceEnum.IQIYI_MOBILE;
    }

    @Override
    public CollectTypeEnum getCollectType() {
        return CollectTypeEnum.ALL;
    }

    @Override
    public Class<MisVideoOrg> getMediaDataType() {
        return MisVideoOrg.class;
    }
}