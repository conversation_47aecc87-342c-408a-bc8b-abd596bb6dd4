package com.heytap.media.iqiyi.collect.spi;

import com.heytap.longvideo.client.media.entity.MisVideoOrg;
import com.heytap.media.common.enums.CollectTypeEnum;
import com.heytap.media.common.enums.SourceEnum;
import com.heytap.media.common.thirdparty.spi.collect.CollectContext;
import com.heytap.media.common.thirdparty.spi.collect.VideoCollectSPI;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 爱奇艺 视频全量采集SPI实现
 * @Author: 80398885WT
 * @Date: 2025/07/17
 */
@Slf4j
@Service
public class IqiyiVideoAllCollectSPI implements VideoCollectSPI {
    
    @Override
    public Map<String, String> getParams(CollectContext collectContext) {
        Map<String, String> params = new HashMap<>();
        // TODO: 根据爱奇艺API要求添加视频采集参数
        return params;
    }

    @Override
    public SourceEnum getCpSource() {
        return SourceEnum.IQIYI_MOBILE;
    }

    @Override
    public CollectTypeEnum getCollectType() {
        return CollectTypeEnum.ALL;
    }

    @Override
    public Class<MisVideoOrg> getMediaDataType() {
        return MisVideoOrg.class;
    }
}