package com.heytap.media.iqiyi.collect.spi;

import com.heytap.media.common.enums.CollectTypeEnum;
import com.heytap.media.common.thirdparty.spi.collect.CollectContext;
import com.heytap.media.iqiyi.config.CollectConfig;
import com.heytap.media.iqiyi.constant.IqiyiConstant;
import com.heytap.media.iqiyi.util.IqiyiMediaCollectPageHandler;
import com.heytap.media.iqiyi.util.IqiyiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 爱奇艺 剧头增量采集SPI实现
 * @Author: 80398885 WT
 * @Date: 2025/07/17
 */
@Slf4j
@Service
public class IqiyiAlbumIncrCollectSPI extends IqiyiAlbumAllCollectSPI {

    @Resource
    private IqiyiMediaCollectPageHandler iqiyiMediaCollectPageHandler;

    @Resource
    private CollectConfig collectConfig;

    @Override
    public Map<String, String> getParams(CollectContext context) {
        Map<String, String> params = new HashMap<>();

        params.put("apiKey", collectConfig.getApiKey());
        params.put(IqiyiConstant.CATEGORY_ID, String.valueOf(context.getBizMap().get(IqiyiConstant.CATEGORY_ID)));

        LocalDateTime now = LocalDateTime.now();
        params.put("startTime", IqiyiUtil.fetchFormatTime(now.minusMinutes(5)));
        params.put("endTime", IqiyiUtil.fetchFormatTime(now));

        if (!Objects.equals(context.getPageNo(), 1)) {
            params.put("minId", String.valueOf(context.getBizMap().get("minId")));
        }

        params.put("pageSize", collectConfig.getAlbumPageSize());

        // 电影频道solo传1
        if (Objects.equals(params.get(IqiyiConstant.CATEGORY_ID), "1")) {
            params.put("solo", "1");
        }

        params.put("status", "1");
        params.put("ts", IqiyiUtil.fetchFormatTime(now));
        return params;
    }

    @Override
    public CollectTypeEnum getCollectType() {
        return CollectTypeEnum.INCREMENT;
    }

    @Override
    public void postHandle(CollectContext context, Exception e) {
        // 处理分页信息
        iqiyiMediaCollectPageHandler.handlePageInfoProxy(context);
    }
}