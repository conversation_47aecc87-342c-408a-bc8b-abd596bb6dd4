package com.heytap.media.iqiyi.collect.spi;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Sets;
import com.heytap.longvideo.client.media.entity.MisAlbumOrg;
import com.heytap.media.common.enums.CollectTypeEnum;
import com.heytap.media.common.enums.SourceEnum;
import com.heytap.media.common.thirdparty.spi.collect.AlbumCollectSPI;
import com.heytap.media.common.thirdparty.spi.collect.CollectContext;
import com.heytap.media.iqiyi.config.CollectConfig;
import com.heytap.media.iqiyi.constant.IqiyiConstant;
import com.heytap.media.iqiyi.util.IqiyiMediaCollectPageHandler;
import com.heytap.media.iqiyi.util.IqiyiUtil;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 爱奇艺 剧头全量采集SPI实现
 * @Author: 80398885 WT
 * @Date: 2025/07/17
 */
@Service
@Slf4j
public class IqiyiAlbumAllCollectSPI implements AlbumCollectSPI {

    // 常量定义
    private static final int VALID_PLATFORM_ID = 10;
    private static final int VALID_AVAILABLE_STATUS = 1;
    private static final int MOVIE_CHANNEL_ID = 1;
    private static final int MIN_MOVIE_DURATION = 600;
    private static final Set<Integer> VALID_CONTENT_TYPES = Sets.newHashSet(1, 3);
    private static final int UGC_FLAG = 1;

    // JSON字段名常量
    private static final String FIELD_FIRST_VIDEO = "firstVideo";
    private static final String FIELD_H5_URL = "h5Url";
    private static final String FIELD_PLAY_CONTROLS = "playControls";
    private static final String FIELD_PUGC = "pugc";
    private static final String FIELD_RUGC = "rugc";
    private static final String FIELD_CONTENT_TYPE = "contentType";
    private static final String FIELD_NAME = "name";
    private static final String FIELD_IMAGE_URL = "imageUrl";
    private static final String FIELD_CHANNEL_ID = "channelId";
    private static final String FIELD_ID = "id";
    private static final String FIELD_FEATURE_ALBUM_ID = "featureAlbumId";
    private static final String FIELD_DURATION = "duration";

    @Resource
    private IqiyiMediaCollectPageHandler iqiyiMediaCollectPageHandler;

    @Resource
    private CollectConfig collectConfig;

    @Override
    public Map<String, String> getParams(CollectContext context) {
        Map<String, String> params = new HashMap<>();
        if (!Objects.equals(context.getPageNo(), 1)) {
            params.put("minId", String.valueOf(context.getBizMap().get("minId")));
        }

        params.put("apiKey", collectConfig.getApiKey());
        params.put(IqiyiConstant.CATEGORY_ID, String.valueOf(context.getBizMap().get(IqiyiConstant.CATEGORY_ID)));
        params.put("pageSize", collectConfig.getAlbumPageSize());

        // 电影频道solo传1
        if (Objects.equals(params.get(IqiyiConstant.CATEGORY_ID), "1")) {
            params.put("solo", "1");
        }

        params.put("status", "1");
        params.put("ts", IqiyiUtil.fetchFormatTime(LocalDateTime.now()));
        return params;
    }

    @Override
    public Map<String, String> getHeaders(CollectContext context) {
        Map<String, String> headers = context.getMediaCollectConfig().getHeaders();
        headers.put("x-sign", IqiyiUtil.getSign((Map<String, String>) context.getBizMap().get("params"), collectConfig.getSecret()));
        return headers;
    }

    @Override
    public SourceEnum getCpSource() {
        return SourceEnum.IQIYI_MOBILE;
    }

    @Override
    public CollectTypeEnum getCollectType() {
        return CollectTypeEnum.ALL;
    }

    @Override
    public Class<MisAlbumOrg> getMediaDataType() {
        return MisAlbumOrg.class;
    }

    @Override
    public boolean filter(MisAlbumOrg mediaOrg) {
        try {
            // 基础验证
            if (isInvalidInput(mediaOrg)) {
                log.debug("过滤数据：输入参数无效");
                return true;
            }

            JsonNode responseJson = parseJsonSafely(mediaOrg.getOriginInfo());
            if (Objects.isNull(responseJson)) {
                log.debug("过滤数据：JSON解析失败");
                return true;
            }

            // 处理 firstVideo 的 h5Url（这个操作修改了原始数据，应该分离）
            processFirstVideoH5Url(mediaOrg, responseJson);

            // 创建过滤上下文，避免重复解析JSON字段
            FilterContext context = new FilterContext(responseJson);

            // 播控验证
            if (shouldFilterByPlayControl(context)) {
                log.debug("过滤数据：播控验证失败");
                return true;
            }

            // 内容类型验证
            if (shouldFilterByContentType(context)) {
                log.debug("过滤数据：内容类型不符合要求");
                return true;
            }

            // 关键字段验证
            if (shouldFilterByRequiredFields(context)) {
                log.debug("过滤数据：关键字段缺失");
                return true;
            }

            // 短视频过滤
            if (shouldFilterByShortVideo(context)) {
                log.debug("过滤数据：短视频规则");
                return true;
            }

            // 电影频道短视频过滤
            boolean shouldFilter = shouldFilterByMovieChannelShortVideo(context);
            if (shouldFilter) {
                log.debug("过滤数据：电影频道短视频");
            }
            return shouldFilter;

        } catch (Exception e) {
            log.error("过滤数据时发生异常", e);
            return true; // 异常情况下过滤掉数据
        }
    }

    /**
     * 过滤上下文，缓存常用的JSON字段访问
     */
    private static class FilterContext {
        private final JsonNode responseJson;
        private final Map<String, JsonNode> fieldCache = new HashMap<>();

        public FilterContext(JsonNode responseJson) {
            this.responseJson = responseJson;
        }

        public JsonNode getField(String fieldName) {
            return fieldCache.computeIfAbsent(fieldName, responseJson::get);
        }

        public Integer getIntField(String fieldName) {
            JsonNode field = getField(fieldName);
            return Objects.isNull(field) ? null : field.asInt();
        }

        public String getStringField(String fieldName) {
            JsonNode field = getField(fieldName);
            return Objects.isNull(field) ? null : field.asText();
        }

        public boolean isFieldEmpty(String fieldName) {
            JsonNode field = getField(fieldName);
            return Objects.isNull(field) || StringUtils.isEmpty(field.asText());
        }
    }

    /**
     * 验证输入参数是否有效
     */
    private boolean isInvalidInput(MisAlbumOrg mediaOrg) {
        return Objects.isNull(mediaOrg) || StringUtils.isEmpty(mediaOrg.getOriginInfo());
    }

    /**
     * 安全地解析JSON
     */
    private JsonNode parseJsonSafely(String jsonString) {
        try {
            return JsonUtil.readTree(jsonString);
        } catch (Exception e) {
            log.warn("JSON解析失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 处理 firstVideo 的 h5Url
     * 注意：这个方法修改了原始数据，在实际应用中应该考虑分离数据处理和过滤逻辑
     */
    private void processFirstVideoH5Url(MisAlbumOrg mediaOrg, JsonNode responseJson) {
        try {
            JsonNode firstVideo = responseJson.get(FIELD_FIRST_VIDEO);
            if (Objects.isNull(firstVideo)) {
                return;
            }

            JsonNode h5Url = firstVideo.get(FIELD_H5_URL);
            if (Objects.isNull(h5Url)) {
                return;
            }

            Map<String, Object> originInfoMap = JsonUtil.fromStr(mediaOrg.getOriginInfo(), new TypeReference<Map<String, Object>>() {});
            Map<String, Object> firstVideoMap = JsonUtil.fromStr(JsonUtil.toJson(firstVideo), new TypeReference<Map<String, Object>>() {});
            originInfoMap.put(FIELD_H5_URL, String.valueOf(firstVideoMap.get(FIELD_H5_URL)));
            mediaOrg.setOriginInfo(JsonUtil.toJson(originInfoMap));
        } catch (Exception e) {
            log.warn("处理firstVideo h5Url时发生异常", e);
        }
    }

    /**
     * 根据播控信息判断是否需要过滤
     */
    private boolean shouldFilterByPlayControl(FilterContext context) {
        JsonNode playControlsNode = context.getField(FIELD_PLAY_CONTROLS);
        if (Objects.isNull(playControlsNode)) {
            return false;
        }

        try {
            List<PlayControl> playControls = JsonUtil.fromStr(JsonUtil.toJson(playControlsNode), new TypeReference<List<PlayControl>>() {});
            if (CollectionUtils.isEmpty(playControls)) {
                return true;
            }

            return !hasValidPlayControl(playControls);
        } catch (Exception e) {
            log.warn("解析播控信息时发生异常", e);
            return true; // 解析失败时过滤
        }
    }

    /**
     * 检查是否有有效的播控
     */
    private boolean hasValidPlayControl(List<PlayControl> playControls) {
        return playControls.stream().anyMatch(this::isValidPlayControl);
    }

    /**
     * 判断播控是否有效
     */
    private boolean isValidPlayControl(PlayControl playControl) {
        return Objects.equals(playControl.getPlatformId(), VALID_PLATFORM_ID)
                && Objects.equals(playControl.getAvailableStatus(), VALID_AVAILABLE_STATUS);
    }

    /**
     * 根据内容类型判断是否需要过滤
     */
    private boolean shouldFilterByContentType(FilterContext context) {
        // 当rugc或pugc至少有一为1时需要过滤
        if (shouldFilterByUgcType(context)) {
            return true;
        }

        // 过滤contentType not in (1, 3)的内容
        return shouldFilterByContentTypeValue(context);
    }

    /**
     * 根据UGC类型判断是否需要过滤
     */
    private boolean shouldFilterByUgcType(FilterContext context) {
        Integer pugc = context.getIntField(FIELD_PUGC);
        Integer rugc = context.getIntField(FIELD_RUGC);

        if (Objects.isNull(pugc) || Objects.isNull(rugc)) {
            return false;
        }

        return Objects.equals(rugc, UGC_FLAG) || Objects.equals(pugc, UGC_FLAG);
    }

    /**
     * 根据contentType值判断是否需要过滤
     */
    private boolean shouldFilterByContentTypeValue(FilterContext context) {
        Integer contentType = context.getIntField(FIELD_CONTENT_TYPE);
        if (Objects.isNull(contentType)) {
            return false;
        }

        return !VALID_CONTENT_TYPES.contains(contentType);
    }

    /**
     * 根据关键字段判断是否需要过滤
     */
    private boolean shouldFilterByRequiredFields(FilterContext context) {
        return context.isFieldEmpty(FIELD_NAME)
                || context.isFieldEmpty(FIELD_H5_URL)
                || context.isFieldEmpty(FIELD_IMAGE_URL);
    }

    /**
     * 根据短视频规则判断是否需要过滤
     */
    private boolean shouldFilterByShortVideo(FilterContext context) {
        Integer channelId = context.getIntField(FIELD_CHANNEL_ID);
        if (Objects.isNull(channelId) || Objects.equals(channelId, MOVIE_CHANNEL_ID)) {
            return false;
        }

        String id = context.getStringField(FIELD_ID);
        String featureAlbumId = context.getStringField(FIELD_FEATURE_ALBUM_ID);

        if (Objects.isNull(id) || Objects.isNull(featureAlbumId)) {
            return false;
        }

        return Objects.equals(id, featureAlbumId);
    }

    /**
     * 根据电影频道短视频规则判断是否需要过滤
     */
    private boolean shouldFilterByMovieChannelShortVideo(FilterContext context) {
        Integer channelId = context.getIntField(FIELD_CHANNEL_ID);
        if (Objects.isNull(channelId) || !Objects.equals(channelId, MOVIE_CHANNEL_ID)) {
            return false;
        }

        Integer duration = context.getIntField(FIELD_DURATION);
        if (Objects.isNull(duration)) {
            return false;
        }

        return duration < MIN_MOVIE_DURATION;
    }

    @Override
    public void postHandle(CollectContext context, Exception e) {
        // 处理分页信息
        iqiyiMediaCollectPageHandler.handlePageInfoProxy(context);
    }

    @Override
    public void afterResolved(MisAlbumOrg mediaOrg) {
        mediaOrg.setProgramType(IqiyiConstant.ProgramTypeEnum.getProgramTypeByChannelId(mediaOrg.getProgramType()));
    }

    @Data
    public static class PlayControl {
        private Integer platformId;
        private Integer downloadAllowed;
        private Integer cooperationAllowed;
        private Integer availableStatus;
        private Integer twAvailableStatus;
    }
}