package com.heytap.media.iqiyi.collect.spi;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.heytap.longvideo.client.media.entity.MisAlbumOrg;
import com.heytap.media.common.enums.CollectTypeEnum;
import com.heytap.media.common.enums.SourceEnum;
import com.heytap.media.common.thirdparty.spi.collect.AlbumCollectSPI;
import com.heytap.media.common.thirdparty.spi.collect.CollectContext;
import com.heytap.media.iqiyi.config.CollectConfig;
import com.heytap.media.iqiyi.constant.IqiyiConstant;
import com.heytap.media.iqiyi.util.IqiyiMediaCollectPageHandler;
import com.heytap.media.iqiyi.util.IqiyiUtil;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 爱奇艺 剧头全量采集SPI实现
 * @Author: 80398885 WT
 * @Date: 2025/07/17
 */
@Service
@Slf4j
public class IqiyiAlbumAllCollectSPI implements AlbumCollectSPI {

    @Resource
    private IqiyiMediaCollectPageHandler iqiyiMediaCollectPageHandler;

    @Resource
    private CollectConfig collectConfig;

    @Override
    public Map<String, String> getParams(CollectContext context) {
        Map<String, String> params = new HashMap<>();
        if (!Objects.equals(context.getPageNo(), 1)) {
            params.put("minId", String.valueOf(context.getBizMap().get("minId")));
        }

        params.put("apiKey", collectConfig.getApiKey());
        params.put(IqiyiConstant.CATEGORY_ID, String.valueOf(context.getBizMap().get(IqiyiConstant.CATEGORY_ID)));
        params.put("pageSize", collectConfig.getAlbumPageSize());

        // 电影频道solo传1
        if (Objects.equals(params.get(IqiyiConstant.CATEGORY_ID), "1")) {
            params.put("solo", "1");
        }

        params.put("status", "1");
        params.put("ts", IqiyiUtil.fetchFormatTime(LocalDateTime.now()));
        return params;
    }

    @Override
    public Map<String, String> getHeaders(CollectContext context) {
        Map<String, String> headers = context.getMediaCollectConfig().getHeaders();
        headers.put("x-sign", IqiyiUtil.getSign((Map<String, String>) context.getBizMap().get("params"), collectConfig.getSecret()));
        return headers;
    }

    @Override
    public SourceEnum getCpSource() {
        return SourceEnum.IQIYI_MOBILE;
    }

    @Override
    public CollectTypeEnum getCollectType() {
        return CollectTypeEnum.ALL;
    }

    @Override
    public Class<MisAlbumOrg> getMediaDataType() {
        return MisAlbumOrg.class;
    }

    @Override
    public boolean filter(MisAlbumOrg mediaOrg) {
        if (Objects.isNull(mediaOrg) || StringUtils.isEmpty(mediaOrg.getOriginInfo())) {
            return true;
        }

        JsonNode responseJson = JsonUtil.readTree(mediaOrg.getOriginInfo());
        if (Objects.isNull(responseJson)) {
            return true;
        }

        if (Objects.nonNull(responseJson.get("firstVideo")) && Objects.nonNull(responseJson.get("firstVideo").get("h5Url"))) {
            Map<String, Object> originInfoMap = JsonUtil.fromStr(mediaOrg.getOriginInfo(), new TypeReference<Map<String, Object>>() {});
            Map<String, Object> firstVideoMap = JsonUtil.fromStr(JsonUtil.toJson(responseJson.get("firstVideo")), new TypeReference<Map<String, Object>>() {});
            originInfoMap.put("h5Url", String.valueOf(firstVideoMap.get("h5Url")));
            mediaOrg.setOriginInfo(JsonUtil.toJson(originInfoMap));
        }

        // 播控相关
        boolean hasPlayControl = false;
        if (Objects.nonNull(responseJson.get("playControls"))) {
            List<PlayControl> playControls = JsonUtil.fromStr(JsonUtil.toJson(responseJson.get("playControls")), new TypeReference<List<PlayControl>>() {});
            // 没有播控需要过滤
            if (CollectionUtils.isEmpty(playControls)) {
                return true;
            }

            for (PlayControl playControl : playControls) {
                if (Objects.equals(playControl.getPlatformId(), 10) && Objects.equals(playControl.getAvailableStatus(), 1)) {
                    hasPlayControl = true;
                    break;
                }
            }

            // 没有满足platForm=10 && availableStatus=1也需要过滤
            if (!hasPlayControl) {
                return true;
            }
        }

        // 当rugc或pugc至少有一为1时需要过滤
        if (Objects.nonNull(responseJson.get("pugc"))
                && Objects.nonNull(responseJson.get("rugc"))
                && (Objects.equals(responseJson.get("rugc").asInt(), 1) || Objects.equals(responseJson.get("pugc").asInt(), 1))) {
            return true;
        }

        // 过滤关键字段为空的数据
        if (Objects.isNull(responseJson.get("name")) || StringUtils.isEmpty(responseJson.get("name").asText())
                || Objects.isNull(responseJson.get("h5Url")) || StringUtils.isEmpty(responseJson.get("h5Url").asText())
                || Objects.isNull(responseJson.get("imageUrl")) || StringUtils.isEmpty(responseJson.get("imageUrl").asText())) {
            return true;
        }

        // 过滤短视频
        if (Objects.nonNull(responseJson.get("channelId"))
                && !Objects.equals(responseJson.get("channelId").asInt(), 1)
                && Objects.nonNull(responseJson.get("id"))
                && Objects.nonNull(responseJson.get("featureAlbumId"))
                && Objects.equals(responseJson.get("id").asText(), responseJson.get("featureAlbumId").asText())) {
            return true;
        }

        // 过滤contentType not in (1, 3)的内容
        if (Objects.nonNull(responseJson.get("contentType"))
                && !Objects.equals(responseJson.get("contentType").asInt(), 1)
                && !Objects.equals(responseJson.get("contentType").asInt(), 3)) {
            return true;
        }

        // 过滤电影频道短视频
        return Objects.nonNull(responseJson.get("channelId")) && Objects.equals(responseJson.get("channelId").asInt(), 1)
                && Objects.nonNull(responseJson.get("duration")) && responseJson.get("duration").asInt() < 600;
    }

    @Override
    public void postHandle(CollectContext context, Exception e) {
        // 处理分页信息
        iqiyiMediaCollectPageHandler.handlePageInfoProxy(context);
    }

    @Override
    public void afterResolved(MisAlbumOrg mediaOrg) {
        mediaOrg.setProgramType(IqiyiConstant.ProgramTypeEnum.getProgramTypeByChannelId(mediaOrg.getProgramType()));
    }

    @Data
    public static class PlayControl {
        private Integer platformId;
        private Integer downloadAllowed;
        private Integer cooperationAllowed;
        private Integer availableStatus;
        private Integer twAvailableStatus;
    }
}