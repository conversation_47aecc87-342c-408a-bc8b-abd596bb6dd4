package com.heytap.media.iqiyi.collect.spi;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.heytap.longvideo.client.media.entity.MisAlbumOrg;
import com.heytap.media.common.enums.CollectTypeEnum;
import com.heytap.media.common.enums.SourceEnum;
import com.heytap.media.common.thirdparty.spi.collect.AlbumCollectSPI;
import com.heytap.media.common.thirdparty.spi.collect.CollectContext;
import com.heytap.media.iqiyi.config.CollectConfig;
import com.heytap.media.iqiyi.constant.IqiyiConstant;
import com.heytap.media.iqiyi.util.IqiyiMediaCollectPageHandler;
import com.heytap.media.iqiyi.util.IqiyiUtil;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 爱奇艺 剧头全量采集SPI实现
 * @Author: 80398885 WT
 * @Date: 2025/07/17
 */
@Service
@Slf4j
public class IqiyiAlbumAllCollectSPI implements AlbumCollectSPI {

    @Resource
    private IqiyiMediaCollectPageHandler iqiyiMediaCollectPageHandler;

    @Resource
    private CollectConfig collectConfig;

    @Override
    public Map<String, String> getParams(CollectContext context) {
        Map<String, String> params = new HashMap<>();
        if (!Objects.equals(context.getPageNo(), 1)) {
            params.put("minId", String.valueOf(context.getBizMap().get("minId")));
        }

        params.put("apiKey", collectConfig.getApiKey());
        params.put(IqiyiConstant.CATEGORY_ID, String.valueOf(context.getBizMap().get(IqiyiConstant.CATEGORY_ID)));
        params.put("pageSize", collectConfig.getAlbumPageSize());

        // 电影频道solo传1
        if (Objects.equals(params.get(IqiyiConstant.CATEGORY_ID), "1")) {
            params.put("solo", "1");
        }

        params.put("status", "1");
        params.put("ts", IqiyiUtil.fetchFormatTime(LocalDateTime.now()));
        return params;
    }

    @Override
    public Map<String, String> getHeaders(CollectContext context) {
        Map<String, String> headers = context.getMediaCollectConfig().getHeaders();
        headers.put("x-sign", IqiyiUtil.getSign((Map<String, String>) context.getBizMap().get("params"), collectConfig.getSecret()));
        return headers;
    }

    @Override
    public SourceEnum getCpSource() {
        return SourceEnum.IQIYI_MOBILE;
    }

    @Override
    public CollectTypeEnum getCollectType() {
        return CollectTypeEnum.ALL;
    }

    @Override
    public Class<MisAlbumOrg> getMediaDataType() {
        return MisAlbumOrg.class;
    }

    @Override
    public boolean filter(MisAlbumOrg mediaOrg) {
        // 基础验证
        if (isInvalidInput(mediaOrg)) {
            return true;
        }

        JsonNode responseJson = JsonUtil.readTree(mediaOrg.getOriginInfo());
        if (Objects.isNull(responseJson)) {
            return true;
        }

        // 处理 firstVideo 的 h5Url
        processFirstVideoH5Url(mediaOrg, responseJson);

        // 播控验证
        if (shouldFilterByPlayControl(responseJson)) {
            return true;
        }

        // 内容类型验证
        if (shouldFilterByContentType(responseJson)) {
            return true;
        }

        // 关键字段验证
        if (shouldFilterByRequiredFields(responseJson)) {
            return true;
        }

        // 短视频过滤
        if (shouldFilterByShortVideo(responseJson)) {
            return true;
        }

        // 电影频道短视频过滤
        return shouldFilterByMovieChannelShortVideo(responseJson);
    }

    /**
     * 验证输入参数是否有效
     */
    private boolean isInvalidInput(MisAlbumOrg mediaOrg) {
        return Objects.isNull(mediaOrg) || StringUtils.isEmpty(mediaOrg.getOriginInfo());
    }

    /**
     * 处理 firstVideo 的 h5Url
     */
    private void processFirstVideoH5Url(MisAlbumOrg mediaOrg, JsonNode responseJson) {
        JsonNode firstVideo = responseJson.get("firstVideo");
        if (Objects.isNull(firstVideo)) {
            return;
        }

        JsonNode h5Url = firstVideo.get("h5Url");
        if (Objects.isNull(h5Url)) {
            return;
        }

        Map<String, Object> originInfoMap = JsonUtil.fromStr(mediaOrg.getOriginInfo(), new TypeReference<Map<String, Object>>() {});
        Map<String, Object> firstVideoMap = JsonUtil.fromStr(JsonUtil.toJson(firstVideo), new TypeReference<Map<String, Object>>() {});
        originInfoMap.put("h5Url", String.valueOf(firstVideoMap.get("h5Url")));
        mediaOrg.setOriginInfo(JsonUtil.toJson(originInfoMap));
    }

    /**
     * 根据播控信息判断是否需要过滤
     */
    private boolean shouldFilterByPlayControl(JsonNode responseJson) {
        JsonNode playControlsNode = responseJson.get("playControls");
        if (Objects.isNull(playControlsNode)) {
            return false;
        }

        List<PlayControl> playControls = JsonUtil.fromStr(JsonUtil.toJson(playControlsNode), new TypeReference<List<PlayControl>>() {});
        if (CollectionUtils.isEmpty(playControls)) {
            return true;
        }

        return !hasValidPlayControl(playControls);
    }

    /**
     * 检查是否有有效的播控
     */
    private boolean hasValidPlayControl(List<PlayControl> playControls) {
        for (PlayControl playControl : playControls) {
            if (isValidPlayControl(playControl)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断播控是否有效
     */
    private boolean isValidPlayControl(PlayControl playControl) {
        return Objects.equals(playControl.getPlatformId(), 10)
                && Objects.equals(playControl.getAvailableStatus(), 1);
    }

    /**
     * 根据内容类型判断是否需要过滤
     */
    private boolean shouldFilterByContentType(JsonNode responseJson) {
        // 当rugc或pugc至少有一为1时需要过滤
        if (shouldFilterByUgcType(responseJson)) {
            return true;
        }

        // 过滤contentType not in (1, 3)的内容
        return shouldFilterByContentTypeValue(responseJson);
    }

    /**
     * 根据UGC类型判断是否需要过滤
     */
    private boolean shouldFilterByUgcType(JsonNode responseJson) {
        JsonNode pugc = responseJson.get("pugc");
        JsonNode rugc = responseJson.get("rugc");

        if (Objects.isNull(pugc) || Objects.isNull(rugc)) {
            return false;
        }

        return Objects.equals(rugc.asInt(), 1) || Objects.equals(pugc.asInt(), 1);
    }

    /**
     * 根据contentType值判断是否需要过滤
     */
    private boolean shouldFilterByContentTypeValue(JsonNode responseJson) {
        JsonNode contentType = responseJson.get("contentType");
        if (Objects.isNull(contentType)) {
            return false;
        }

        int contentTypeValue = contentType.asInt();
        return contentTypeValue != 1 && contentTypeValue != 3;
    }

    /**
     * 根据关键字段判断是否需要过滤
     */
    private boolean shouldFilterByRequiredFields(JsonNode responseJson) {
        return isFieldEmpty(responseJson, "name")
                || isFieldEmpty(responseJson, "h5Url")
                || isFieldEmpty(responseJson, "imageUrl");
    }

    /**
     * 检查字段是否为空
     */
    private boolean isFieldEmpty(JsonNode responseJson, String fieldName) {
        JsonNode field = responseJson.get(fieldName);
        return Objects.isNull(field) || StringUtils.isEmpty(field.asText());
    }

    /**
     * 根据短视频规则判断是否需要过滤
     */
    private boolean shouldFilterByShortVideo(JsonNode responseJson) {
        JsonNode channelId = responseJson.get("channelId");
        if (Objects.isNull(channelId) || Objects.equals(channelId.asInt(), 1)) {
            return false;
        }

        JsonNode id = responseJson.get("id");
        JsonNode featureAlbumId = responseJson.get("featureAlbumId");

        if (Objects.isNull(id) || Objects.isNull(featureAlbumId)) {
            return false;
        }

        return Objects.equals(id.asText(), featureAlbumId.asText());
    }

    /**
     * 根据电影频道短视频规则判断是否需要过滤
     */
    private boolean shouldFilterByMovieChannelShortVideo(JsonNode responseJson) {
        JsonNode channelId = responseJson.get("channelId");
        if (Objects.isNull(channelId) || !Objects.equals(channelId.asInt(), 1)) {
            return false;
        }

        JsonNode duration = responseJson.get("duration");
        if (Objects.isNull(duration)) {
            return false;
        }

        return duration.asInt() < 600;
    }

    @Override
    public void postHandle(CollectContext context, Exception e) {
        // 处理分页信息
        iqiyiMediaCollectPageHandler.handlePageInfoProxy(context);
    }

    @Override
    public void afterResolved(MisAlbumOrg mediaOrg) {
        mediaOrg.setProgramType(IqiyiConstant.ProgramTypeEnum.getProgramTypeByChannelId(mediaOrg.getProgramType()));
    }

    @Data
    public static class PlayControl {
        private Integer platformId;
        private Integer downloadAllowed;
        private Integer cooperationAllowed;
        private Integer availableStatus;
        private Integer twAvailableStatus;
    }
}