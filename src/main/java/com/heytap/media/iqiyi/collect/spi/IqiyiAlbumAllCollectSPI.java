package com.heytap.media.iqiyi.collect.spi;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.heytap.longvideo.client.media.entity.MisAlbumOrg;
import com.heytap.media.common.enums.CollectTypeEnum;
import com.heytap.media.common.enums.SourceEnum;
import com.heytap.media.common.thirdparty.spi.collect.AlbumCollectSPI;
import com.heytap.media.common.thirdparty.spi.collect.CollectContext;
import com.heytap.media.iqiyi.config.CollectConfig;
import com.heytap.media.iqiyi.constant.IqiyiConstant;
import com.heytap.media.iqiyi.util.IqiyiMediaCollectPageHandler;
import com.heytap.media.iqiyi.util.IqiyiUtil;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;

/**
 * 爱奇艺 剧头全量采集SPI实现
 * @Author: 80398885 WT
 * @Date: 2025/07/17
 */
@Service
@Slf4j
public class IqiyiAlbumAllCollectSPI implements AlbumCollectSPI {

    // 常量定义
    private static final int VALID_PLATFORM_ID = 10;
    private static final int VALID_AVAILABLE_STATUS = 1;
    private static final int MOVIE_CHANNEL_ID = 1;
    private static final int MIN_MOVIE_DURATION = 600;
    private static final int UGC_FLAG = 1;

    // Java 8 兼容的集合初始化
    private static final Set<Integer> VALID_CONTENT_TYPES;
    static {
        Set<Integer> types = new HashSet<>();
        types.add(1);
        types.add(3);
        VALID_CONTENT_TYPES = Collections.unmodifiableSet(types);
    }

    // JSON字段名常量
    private static final String FIELD_FIRST_VIDEO = "firstVideo";
    private static final String FIELD_H5_URL = "h5Url";
    private static final String FIELD_PLAY_CONTROLS = "playControls";
    private static final String FIELD_PUGC = "pugc";
    private static final String FIELD_RUGC = "rugc";
    private static final String FIELD_CONTENT_TYPE = "contentType";
    private static final String FIELD_NAME = "name";
    private static final String FIELD_IMAGE_URL = "imageUrl";
    private static final String FIELD_CHANNEL_ID = "channelId";
    private static final String FIELD_ID = "id";
    private static final String FIELD_FEATURE_ALBUM_ID = "featureAlbumId";
    private static final String FIELD_DURATION = "duration";

    @Resource
    private IqiyiMediaCollectPageHandler iqiyiMediaCollectPageHandler;

    @Resource
    private CollectConfig collectConfig;

    @Override
    public Map<String, String> getParams(CollectContext context) {
        Map<String, String> params = new HashMap<>();

        // 使用 Optional 进行安全的参数处理
        Optional.of(context.getPageNo())
                .filter(pageNo -> !Objects.equals(pageNo, 1))
                .flatMap(pageNo -> Optional.ofNullable(context.getBizMap().get("minId")).map(String::valueOf))
                .ifPresent(minId -> params.put("minId", minId));

        // 基础参数设置
        params.put("apiKey", collectConfig.getApiKey());
        params.put("pageSize", collectConfig.getAlbumPageSize());
        params.put("status", "1");
        params.put("ts", IqiyiUtil.fetchFormatTime(LocalDateTime.now()));

        // 分类ID处理
        Optional.ofNullable(context.getBizMap().get(IqiyiConstant.CATEGORY_ID))
                .map(String::valueOf)
                .ifPresent(categoryId -> {
                    params.put(IqiyiConstant.CATEGORY_ID, categoryId);
                    // 电影频道特殊处理
                    if ("1".equals(categoryId)) {
                        params.put("solo", "1");
                    }
                });

        return params;
    }

    @Override
    public Map<String, String> getHeaders(CollectContext context) {
        Map<String, String> headers = context.getMediaCollectConfig().getHeaders();
        headers.put("x-sign", IqiyiUtil.getSign((Map<String, String>) context.getBizMap().get("params"), collectConfig.getSecret()));
        return headers;
    }

    @Override
    public SourceEnum getCpSource() {
        return SourceEnum.IQIYI_MOBILE;
    }

    @Override
    public CollectTypeEnum getCollectType() {
        return CollectTypeEnum.ALL;
    }

    @Override
    public Class<MisAlbumOrg> getMediaDataType() {
        return MisAlbumOrg.class;
    }

    @Override
    public boolean filter(MisAlbumOrg mediaOrg) {
        return Optional.ofNullable(mediaOrg)
                .filter(this::isValidInput)
                .map(MisAlbumOrg::getOriginInfo)
                .map(this::parseJsonSafely)
                .filter(Objects::nonNull)
                .map(responseJson -> {
                    try {
                        // 处理 firstVideo 的 h5Url（副作用操作）
                        processFirstVideoH5Url(mediaOrg, responseJson);

                        // 创建过滤上下文
                        FilterContext context = new FilterContext(responseJson);

                        // 使用函数式编程进行过滤链
                        return applyFilterChain(context);

                    } catch (Exception e) {
                        log.error("过滤数据时发生异常", e);
                        return FilterResult.FILTER; // 异常情况下过滤
                    }
                })
                .orElse(FilterResult.FILTER)
                .shouldFilter();
    }

    /**
     * 过滤结果枚举
     */
    @Getter
    private enum FilterResult {
        FILTER(true, "过滤"),
        PASS(false, "通过");

        private final boolean shouldFilter;
        private final String description;

        FilterResult(boolean shouldFilter, String description) {
            this.shouldFilter = shouldFilter;
            this.description = description;
        }

        public boolean shouldFilter() {
            return shouldFilter;
        }
    }

    /**
     * 应用过滤链 - Java 8 函数式风格
     */
    private FilterResult applyFilterChain(FilterContext context) {
        // 定义过滤规则链
        List<FilterRule> filterRules = Arrays.asList(
            new FilterRule("播控验证", this::shouldFilterByPlayControl),
            new FilterRule("内容类型验证", this::shouldFilterByContentType),
            new FilterRule("关键字段验证", this::shouldFilterByRequiredFields),
            new FilterRule("短视频过滤", this::shouldFilterByShortVideo),
            new FilterRule("电影频道短视频", this::shouldFilterByMovieChannelShortVideo)
        );

        // 使用 Stream 进行过滤链处理
        return filterRules.stream()
                .filter(rule -> rule.getPredicate().test(context))
                .findFirst()
                .map(rule -> {
                    log.debug("过滤数据：{}", rule.getRuleName());
                    return FilterResult.FILTER;
                })
                .orElse(FilterResult.PASS);
    }

    /**
     * 过滤规则封装
     */
    @Getter
    private static class FilterRule {
        private final String ruleName;
        private final Predicate<FilterContext> predicate;

        public FilterRule(String ruleName, Predicate<FilterContext> predicate) {
            this.ruleName = ruleName;
            this.predicate = predicate;
        }

    }

    /**
     * 过滤上下文，缓存常用的JSON字段访问 - Java 8 优化版本
     */
    private static class FilterContext {
        private final JsonNode responseJson;
        private final Map<String, JsonNode> fieldCache = new HashMap<>();

        public FilterContext(JsonNode responseJson) {
            this.responseJson = responseJson;
        }

        /**
         * 获取字段值，使用缓存
         */
        public JsonNode getField(String fieldName) {
            return fieldCache.computeIfAbsent(fieldName, responseJson::get);
        }

        /**
         * 安全获取整数字段 - Optional 风格
         */
        public Optional<Integer> getIntField(String fieldName) {
            return Optional.ofNullable(getField(fieldName))
                    .map(JsonNode::asInt);
        }

        /**
         * 安全获取字符串字段 - Optional 风格
         */
        public Optional<String> getStringField(String fieldName) {
            return Optional.ofNullable(getField(fieldName))
                    .map(JsonNode::asText)
                    .filter(StringUtils::isNotEmpty);
        }

        /**
         * 检查字段是否为空
         */
        public boolean isFieldEmpty(String fieldName) {
            return !getStringField(fieldName).isPresent();
        }

    }

    /**
     * 验证输入参数是否有效 - Java 8 Optional 风格
     */
    private boolean isValidInput(MisAlbumOrg mediaOrg) {
        return Optional.ofNullable(mediaOrg)
                .map(MisAlbumOrg::getOriginInfo)
                .filter(StringUtils::isNotEmpty)
                .isPresent();
    }

    /**
     * 安全地解析JSON - Java 8 Optional 风格
     */
    private JsonNode parseJsonSafely(String jsonString) {
        return Optional.ofNullable(jsonString)
                .filter(StringUtils::isNotEmpty)
                .map(json -> {
                    try {
                        return JsonUtil.readTree(json);
                    } catch (Exception e) {
                        log.warn("JSON解析失败: {}", e.getMessage());
                        return null;
                    }
                })
                .orElse(null);
    }

    /**
     * 安全的整数比较
     */
    private boolean isIntegerEquals(Optional<Integer> value, int expected) {
        return value.map(v -> Objects.equals(v, expected)).orElse(false);
    }

    /**
     * 安全的字符串比较
     */
    private boolean isStringEquals(Optional<String> value, String expected) {
        return value.map(v -> Objects.equals(v, expected)).orElse(false);
    }

    /**
     * 处理 firstVideo 的 h5Url
     * 注意：这个方法修改了原始数据，在实际应用中应该考虑分离数据处理和过滤逻辑
     */
    private void processFirstVideoH5Url(MisAlbumOrg mediaOrg, JsonNode responseJson) {
        try {
            JsonNode firstVideo = responseJson.get(FIELD_FIRST_VIDEO);
            if (Objects.isNull(firstVideo)) {
                return;
            }

            JsonNode h5Url = firstVideo.get(FIELD_H5_URL);
            if (Objects.isNull(h5Url)) {
                return;
            }

            Map<String, Object> originInfoMap = JsonUtil.fromStr(mediaOrg.getOriginInfo(), new TypeReference<Map<String, Object>>() {});
            Map<String, Object> firstVideoMap = JsonUtil.fromStr(JsonUtil.toJson(firstVideo), new TypeReference<Map<String, Object>>() {});
            originInfoMap.put(FIELD_H5_URL, String.valueOf(firstVideoMap.get(FIELD_H5_URL)));
            mediaOrg.setOriginInfo(JsonUtil.toJson(originInfoMap));
        } catch (Exception e) {
            log.warn("处理firstVideo h5Url时发生异常", e);
        }
    }

    /**
     * 根据播控信息判断是否需要过滤 - Java 8 函数式风格
     */
    private boolean shouldFilterByPlayControl(FilterContext context) {
        return Optional.ofNullable(context.getField(FIELD_PLAY_CONTROLS))
                .map(this::parsePlayControls)
                .map(playControls -> {
                    if (CollectionUtils.isEmpty(playControls)) {
                        return true; // 空播控列表需要过滤
                    }
                    return !hasValidPlayControl(playControls);
                })
                .orElse(false); // 没有播控信息不过滤
    }

    /**
     * 解析播控信息
     */
    private List<PlayControl> parsePlayControls(JsonNode playControlsNode) {
        try {
            return JsonUtil.fromStr(JsonUtil.toJson(playControlsNode),
                    new TypeReference<List<PlayControl>>() {});
        } catch (Exception e) {
            log.warn("解析播控信息时发生异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 检查是否有有效的播控 - Java 8 Stream 风格
     */
    private boolean hasValidPlayControl(List<PlayControl> playControls) {
        return Optional.ofNullable(playControls)
                .map(List::stream)
                .map(stream -> stream.anyMatch(this::isValidPlayControl))
                .orElse(false);
    }

    /**
     * 判断播控是否有效 - Optional 风格
     */
    private boolean isValidPlayControl(PlayControl playControl) {
        return Optional.ofNullable(playControl)
                .filter(pc -> Objects.equals(pc.getPlatformId(), VALID_PLATFORM_ID))
                .filter(pc -> Objects.equals(pc.getAvailableStatus(), VALID_AVAILABLE_STATUS))
                .isPresent();
    }

    /**
     * 根据内容类型判断是否需要过滤 - Java 8 函数式风格
     */
    private boolean shouldFilterByContentType(FilterContext context) {
        return shouldFilterByUgcType(context) || shouldFilterByContentTypeValue(context);
    }

    /**
     * 根据UGC类型判断是否需要过滤 - Optional 风格
     */
    private boolean shouldFilterByUgcType(FilterContext context) {
        Optional<Integer> pugc = context.getIntField(FIELD_PUGC);
        Optional<Integer> rugc = context.getIntField(FIELD_RUGC);

        // 只有当两个字段都存在时才进行判断
        return pugc.isPresent() && rugc.isPresent() &&
               (isIntegerEquals(rugc, UGC_FLAG) || isIntegerEquals(pugc, UGC_FLAG));
    }

    /**
     * 根据contentType值判断是否需要过滤 - Optional 风格
     */
    private boolean shouldFilterByContentTypeValue(FilterContext context) {
        return context.getIntField(FIELD_CONTENT_TYPE)
                .map(contentType -> !VALID_CONTENT_TYPES.contains(contentType))
                .orElse(false);
    }

    /**
     * 根据关键字段判断是否需要过滤 - Java 8 Stream 风格
     */
    private boolean shouldFilterByRequiredFields(FilterContext context) {
        String[] requiredFields = {FIELD_NAME, FIELD_H5_URL, FIELD_IMAGE_URL};

        return Arrays.stream(requiredFields)
                .anyMatch(context::isFieldEmpty);
    }

    /**
     * 根据短视频规则判断是否需要过滤 - Optional 链式调用
     */
    private boolean shouldFilterByShortVideo(FilterContext context) {
        return context.getIntField(FIELD_CHANNEL_ID)
                .filter(channelId -> !Objects.equals(channelId, MOVIE_CHANNEL_ID))
                .map(channelId -> isShortVideoByIds(context))
                .orElse(false);
    }

    /**
     * 通过ID比较判断是否为短视频
     */
    private boolean isShortVideoByIds(FilterContext context) {
        Optional<String> id = context.getStringField(FIELD_ID);
        Optional<String> featureAlbumId = context.getStringField(FIELD_FEATURE_ALBUM_ID);

        return id.isPresent() && featureAlbumId.isPresent() &&
               isStringEquals(id, featureAlbumId.get());
    }

    /**
     * 根据电影频道短视频规则判断是否需要过滤 - Optional 链式调用
     */
    private boolean shouldFilterByMovieChannelShortVideo(FilterContext context) {
        return context.getIntField(FIELD_CHANNEL_ID)
                .filter(channelId -> Objects.equals(channelId, MOVIE_CHANNEL_ID))
                .flatMap(channelId -> context.getIntField(FIELD_DURATION))
                .map(duration -> duration < MIN_MOVIE_DURATION)
                .orElse(false);
    }

    @Override
    public void postHandle(CollectContext context, Exception e) {
        // 处理分页信息
        iqiyiMediaCollectPageHandler.handlePageInfoProxy(context);
    }

    @Override
    public void afterResolved(MisAlbumOrg mediaOrg) {
        mediaOrg.setProgramType(IqiyiConstant.ProgramTypeEnum.getProgramTypeByChannelId(mediaOrg.getProgramType()));
    }

    @Data
    public static class PlayControl {
        private Integer platformId;
        private Integer downloadAllowed;
        private Integer cooperationAllowed;
        private Integer availableStatus;
        private Integer twAvailableStatus;
    }
}