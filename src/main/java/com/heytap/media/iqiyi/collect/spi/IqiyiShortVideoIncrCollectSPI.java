package com.heytap.media.iqiyi.collect.spi;

import com.heytap.media.common.enums.CollectTypeEnum;
import com.heytap.media.common.thirdparty.spi.collect.CollectContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 爱奇艺 短视频增量采集SPI实现
 * @Author: 80398885 WT
 * @Date: 2025/07/17
 */
@Slf4j
@Service
public class IqiyiShortVideoIncrCollectSPI extends IqiyiShortVideoAllCollectSPI {

    @Override
    public Map<String, String> getParams(CollectContext context) {
        // TODO: 增量采集传什么参数？
        return super.getParams(context);
    }

    @Override
    public CollectTypeEnum getCollectType() {
        return CollectTypeEnum.INCREMENT;
    }
}