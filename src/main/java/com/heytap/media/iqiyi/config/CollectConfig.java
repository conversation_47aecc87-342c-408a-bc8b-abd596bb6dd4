package com.heytap.media.iqiyi.config;

import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

/**
 * @Description: 爱奇艺 采集配置
 * @Author: 80398885 WT
 * @Date: 2025/07/31
 */
@Data
@Configuration
public class CollectConfig {

    @HeraclesDynamicConfig(key = "extendsMap.apiKey", fileName = "original_media_collect_iqiyimobile.yaml")
    private String apiKey;

    @HeraclesDynamicConfig(key = "extendsMap.secret", fileName = "original_media_collect_iqiyimobile.yaml")
    private String secret;

    @HeraclesDynamicConfig(key = "extendsMap.albumPageSize", fileName = "original_media_collect_iqiyimobile.yaml")
    private String albumPageSize;
}