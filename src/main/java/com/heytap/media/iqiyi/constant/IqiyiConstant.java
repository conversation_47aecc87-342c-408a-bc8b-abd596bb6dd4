package com.heytap.media.iqiyi.constant;

import com.heytap.longvideo.client.media.constant.StandardConstant;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 爱奇艺 常量类
 * @Author: 80398885 WT
 * @Date: 2025/07/17
 */
public class IqiyiConstant {

    @Getter
    public enum ProgramTypeEnum {
        MOVIE("1", StandardConstant.ProgramType.MOVIE, "电影"),
        TV("2", StandardConstant.ProgramType.TV, "电视剧"),
        DOC("3", StandardConstant.ProgramType.DOC, "纪录片"),
        COMIC("4", StandardConstant.ProgramType.COMIC, "动漫"),
        MUSIC("5", StandardConstant.ProgramType.MUSIC, "音乐"),
        SHOW("6", StandardConstant.ProgramType.SHOW, "综艺"),
        ENT("7", StandardConstant.ProgramType.ENT, "娱乐"),
        GAME("8", StandardConstant.ProgramType.GAME, "游戏"),
        TOUR("9", StandardConstant.ProgramType.TOUR, "旅游"),
        EDU("12", StandardConstant.ProgramType.EDU, "教育"),
        FASHION("13", StandardConstant.ProgramType.FASHION, "时尚"),
        KIDS("15", StandardConstant.ProgramType.KIDS, "少儿"),
        SPORTS("17", StandardConstant.ProgramType.SPORTS, "体育"),
        LIFE("21", StandardConstant.ProgramType.LIFE, "生活"),
        FUNNY("22", StandardConstant.ProgramType.FUNNY, "搞笑"),
        FINANCE("24", StandardConstant.ProgramType.FINANCE, "财经"),
        NEWS("25", StandardConstant.ProgramType.NEWS, "新闻"),
        CAR("26", StandardConstant.ProgramType.CAR, "汽车"),
        ORIGIN("27", StandardConstant.ProgramType.ORIGIN, "原创"),
        BABY("29", StandardConstant.ProgramType.BABY, "母婴"),
        TEC("30", StandardConstant.ProgramType.TEC, "科技");

        ProgramTypeEnum(String channelId, String programType, String category) {
            this.channelId = channelId;
            this.programType = programType;
            this.category = category;
        }

        private final String channelId;

        private final String programType;

        private final String category;

        public static String getProgramTypeByChannelId(String channelId) {
            if (StringUtils.isEmpty(channelId)) {
                return channelId;
            }

            for (ProgramTypeEnum sourceEnum : ProgramTypeEnum.values()) {
                if (Objects.equals(sourceEnum.getChannelId(), channelId)) {
                    return sourceEnum.getProgramType();
                }
            }

            return channelId;
        }

        public static String getCategoryByChannelId(String channelId) {
            if (StringUtils.isEmpty(channelId)) {
                return channelId;
            }

            for (ProgramTypeEnum sourceEnum : ProgramTypeEnum.values()) {
                if (Objects.equals(sourceEnum.getChannelId(), channelId)) {
                    return sourceEnum.getCategory();
                }
            }

            return channelId;
        }
    }

    public static final String CATEGORY_ID = "categoryId";
}