# 爱奇艺媒资采集和标准化SPI实现

本项目是基于src2（Z视介媒资采集和标准化三方插件）的代码模式，为爱奇艺平台实现的媒资采集和标准化SPI插件。

## 项目结构

```
src/main/java/com/heytap/media/iqiyi/
├── collect/spi/                    # 采集SPI实现
│   ├── IqiyiAlbumAllCollectSPI.java       # 剧头全量采集
│   ├── IqiyiAlbumIncrCollectSPI.java      # 剧头增量采集
│   ├── IqiyiVideoAllCollectSPI.java       # 视频全量采集
│   ├── IqiyiVideoIncrCollectSPI.java      # 视频增量采集
│   ├── IqiyiShortVideoAllCollectSPI.java  # 短视频全量采集
│   └── IqiyiShortVideoIncrCollectSPI.java # 短视频增量采集
├── standard/spi/                   # 标准化SPI实现
│   ├── IqiyiAlbumStandardSPI.java         # 剧头标准化
│   └── IqiyiVideoStandardSPI.java         # 视频标准化
├── config/                         # 配置类
│   └── ProgramTypeConfig.java             # 节目类型映射配置
└── util/                          # 工具类
    ├── IqiyiUtil.java                     # 爱奇艺工具类
    └── IqiyiMediaCollectPageHandler.java  # 分页处理工具

src/main/resources/
└── program_type_config.yaml       # 节目类型配置文件
```

## 主要功能

### 1. 采集SPI实现
- **全量采集**: 支持剧头、视频、短视频的全量数据采集
- **增量采集**: 支持剧头、视频、短视频的增量数据采集
- **分页处理**: 自动处理API分页逻辑
- **参数构建**: 根据爱奇艺API要求构建请求参数

### 2. 标准化SPI实现
- **数据标准化**: 将爱奇艺原始数据转换为标准格式
- **字段映射**: 处理特殊字段映射和数据转换
- **业务规则**: 应用爱奇艺特有的业务处理规则

### 3. 配置管理
- **节目类型映射**: 支持爱奇艺节目类型到标准类型的映射
- **动态配置**: 支持通过配置文件动态调整映射规则
- **正则表达式**: 支持标题、单元等字段的正则匹配处理

### 4. 工具类
- **签名生成**: 支持爱奇艺API签名生成（待实现）
- **标题处理**: 智能提取和处理节目标题
- **分页处理**: 自动处理API响应的分页信息

## 核心特性

1. **Spring集成**: 所有SPI实现都使用@Service注解，支持Spring依赖注入
2. **日志支持**: 使用@Slf4j注解提供完整的日志记录
3. **异常处理**: 完善的异常处理和错误日志记录
4. **配置驱动**: 支持通过配置文件调整业务规则
5. **扩展性**: 预留了TODO标记，便于后续功能扩展

## 待完善功能

以下功能标记为TODO，需要根据爱奇艺实际API进行实现：

1. **API参数**: 完善各种采集场景的API请求参数
2. **签名算法**: 实现爱奇艺API的签名生成算法
3. **响应解析**: 根据实际API响应格式调整解析逻辑
4. **业务规则**: 完善爱奇艺特有的数据处理规则
5. **错误处理**: 完善API错误响应的处理逻辑

## 使用说明

1. 确保项目依赖的media-common-lib和longvideo-media-client-lib版本正确
2. 根据实际的爱奇艺API文档完善TODO标记的功能
3. 配置program_type_config.yaml中的节目类型映射
4. 根据需要调整regex_config.properties中的正则表达式

## 注意事项

- 所有SPI实现都返回SourceEnum.IQIYI作为数据源标识
- 标准化处理中设置了爱奇艺的默认评分为"7"
- 分页处理逻辑需要根据实际API响应格式进行调整
- 签名生成和API参数构建需要参考爱奇艺官方API文档
