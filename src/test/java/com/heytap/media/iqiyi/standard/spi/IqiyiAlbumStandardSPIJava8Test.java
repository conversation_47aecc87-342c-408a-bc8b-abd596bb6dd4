package com.heytap.media.iqiyi.standard.spi;

import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.media.iqiyi.util.IqiyiUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * IqiyiAlbumStandardSPI Java 8 特性测试类
 * 验证 Java 8 兼容性和性能
 */
@ExtendWith(MockitoExtension.class)
class IqiyiAlbumStandardSPIJava8Test {

    @Mock
    private IqiyiUtil iqiyiUtil;

    @InjectMocks
    private IqiyiAlbumStandardSPI standardSPI;

    @BeforeEach
    void setUp() {
        standardSPI.initFieldProcessors();
        when(iqiyiUtil.matchTitle(anyString())).thenAnswer(invocation -> invocation.getArgument(0));
    }

    @Test
    void testJava8Compatibility() {
        // 测试所有 Java 8 特性是否正常工作
        assertTrue(standardSPI.isSpecialMappingField("year"));
        assertTrue(standardSPI.isSpecialMappingField("actor"));
        assertFalse(standardSPI.isSpecialMappingField("unknown"));
    }

    @Test
    void testOptionalChaining() throws NoSuchFieldException {
        StandardAlbum album = new StandardAlbum();
        Field yearField = StandardAlbum.class.getDeclaredField("year");
        
        // 测试 Optional 链式调用
        standardSPI.processSpecialMappingField(album, "2023年的电影", yearField);
        assertEquals(Integer.valueOf(2023), album.getYear());
        
        // 测试 null 安全
        album.setYear(null);
        standardSPI.processSpecialMappingField(album, null, yearField);
        assertNull(album.getYear());
    }

    @Test
    void testStreamProcessing() throws NoSuchFieldException {
        StandardAlbum album = new StandardAlbum();
        Field actorField = StandardAlbum.class.getDeclaredField("actor");
        
        // 测试 Stream 处理复杂字符串
        String complexActorString = "张三, 李四 ,其他, 王五,  , 其它, 赵六, 张三"; // 包含重复、空白、无效值
        standardSPI.processSpecialMappingField(album, complexActorString, actorField);
        
        String result = album.getActor();
        assertNotNull(result);
        
        // 验证去重、过滤、顺序保持
        String[] actors = result.split("\\|");
        assertEquals(4, actors.length); // 张三、李四、王五、赵六
        assertEquals("张三", actors[0]);
        assertEquals("李四", actors[1]);
        assertEquals("王五", actors[2]);
        assertEquals("赵六", actors[3]);
    }

    @Test
    void testCollectionsCompatibility() {
        // 测试 Java 8 兼容的集合初始化
        assertTrue(standardSPI.isSpecialMappingField("year"));
        assertTrue(standardSPI.isSpecialMappingField("tags"));
        assertTrue(standardSPI.isSpecialMappingField("actor"));
        assertTrue(standardSPI.isSpecialMappingField("director"));
        assertTrue(standardSPI.isSpecialMappingField("duration"));
        assertTrue(standardSPI.isSpecialMappingField("featureType"));
    }

    @Test
    void testFeatureTypeMappingJava8() throws NoSuchFieldException {
        StandardAlbum album = new StandardAlbum();
        Field featureTypeField = StandardAlbum.class.getDeclaredField("featureType");
        
        // 测试 Java 8 兼容的 Map 使用
        standardSPI.processSpecialMappingField(album, "1", featureTypeField);
        assertEquals(Integer.valueOf(1), album.getFeatureType());
        
        album.setFeatureType(null);
        standardSPI.processSpecialMappingField(album, "3", featureTypeField);
        assertEquals(Integer.valueOf(2), album.getFeatureType());
    }

    @Test
    void testErrorHandling() throws NoSuchFieldException {
        StandardAlbum album = new StandardAlbum();
        Field durationField = StandardAlbum.class.getDeclaredField("duration");
        
        // 测试异常处理
        standardSPI.processSpecialMappingField(album, "invalid_number", durationField);
        assertEquals(Integer.valueOf(0), album.getDuration()); // 应该设置为默认值
        
        // 测试特殊值处理
        album.setDuration(null);
        standardSPI.processSpecialMappingField(album, "-1", durationField);
        assertEquals(Integer.valueOf(0), album.getDuration());
    }

    @Test
    void testPerformanceWithStreams() throws NoSuchFieldException {
        // 性能测试：处理大量数据
        StandardAlbum album = new StandardAlbum();
        Field actorField = StandardAlbum.class.getDeclaredField("actor");
        
        // 创建包含100个演员的字符串
        StringBuilder actorBuilder = new StringBuilder();
        IntStream.range(0, 100)
                .forEach(i -> {
                    if (i > 0) actorBuilder.append(",");
                    actorBuilder.append("演员").append(i);
                    if (i % 10 == 0) actorBuilder.append(",其他"); // 添加一些无效值
                });
        
        long startTime = System.nanoTime();
        standardSPI.processSpecialMappingField(album, actorBuilder.toString(), actorField);
        long endTime = System.nanoTime();
        
        long durationMs = TimeUnit.NANOSECONDS.toMillis(endTime - startTime);
        System.out.println("处理100个演员耗时: " + durationMs + "ms");
        
        // 验证结果
        assertNotNull(album.getActor());
        String[] actors = album.getActor().split("\\|");
        assertEquals(90, actors.length); // 100 - 10个"其他"
        
        // 性能应该在合理范围内（小于100ms）
        assertTrue(durationMs < 100, "处理时间过长: " + durationMs + "ms");
    }

    @Test
    void testBusinessFieldsOptionalChaining() {
        StandardAlbum album = new StandardAlbum();
        album.setTitle("  测试标题  ");
        album.setCategory(1);
        album.setProgramType(2);
        
        standardSPI.postProcessMappingFields(album);
        
        // 验证 Optional 链式调用的结果
        assertNotNull(album.getTitle());
        assertNotNull(album.getCreateTime());
        assertNotNull(album.getUpdateTime());
        assertEquals(Integer.valueOf(1), album.getStatus());
        assertEquals("iqiyi", album.getCopyrightCode());
    }

    @Test
    void testEdgeCasesWithOptional() throws NoSuchFieldException {
        StandardAlbum album = new StandardAlbum();
        
        // 测试各种边界情况
        Field[] fields = {
            StandardAlbum.class.getDeclaredField("year"),
            StandardAlbum.class.getDeclaredField("duration"),
            StandardAlbum.class.getDeclaredField("actor"),
            StandardAlbum.class.getDeclaredField("featureType")
        };
        
        Object[] testValues = {null, "", "   ", "invalid"};
        
        for (Field field : fields) {
            for (Object testValue : testValues) {
                // 应该不抛出异常
                assertDoesNotThrow(() -> 
                    standardSPI.processSpecialMappingField(album, testValue, field)
                );
            }
        }
    }

    @Test
    void testMemoryEfficiency() throws NoSuchFieldException {
        // 测试内存使用效率
        StandardAlbum album = new StandardAlbum();
        Field actorField = StandardAlbum.class.getDeclaredField("actor");
        
        // 测试大字符串处理
        String largeActorString = IntStream.range(0, 1000)
                .mapToObj(i -> "演员" + i)
                .reduce((a, b) -> a + "," + b)
                .orElse("");
        
        long beforeMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
        
        standardSPI.processSpecialMappingField(album, largeActorString, actorField);
        
        long afterMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
        long memoryUsed = afterMemory - beforeMemory;
        
        System.out.println("处理1000个演员使用内存: " + memoryUsed + " bytes");
        
        // 验证结果正确性
        assertNotNull(album.getActor());
        String[] actors = album.getActor().split("\\|");
        assertEquals(1000, actors.length);
    }

    @Test
    void testConcurrentSafety() {
        // 测试并发安全性（虽然这个类不是线程安全的，但测试基本的并发访问）
        StandardAlbum album = new StandardAlbum();
        
        // 并发调用 postProcessMappingFields
        IntStream.range(0, 10)
                .parallel()
                .forEach(i -> {
                    StandardAlbum testAlbum = new StandardAlbum();
                    testAlbum.setTitle("测试标题" + i);
                    assertDoesNotThrow(() -> standardSPI.postProcessMappingFields(testAlbum));
                });
    }
}
