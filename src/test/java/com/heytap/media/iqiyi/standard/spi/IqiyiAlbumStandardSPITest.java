package com.heytap.media.iqiyi.standard.spi;

import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.media.iqiyi.util.IqiyiUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * IqiyiAlbumStandardSPI 测试类
 * 验证优化后的字段处理逻辑
 */
@ExtendWith(MockitoExtension.class)
class IqiyiAlbumStandardSPITest {

    @Mock
    private IqiyiUtil iqiyiUtil;

    @InjectMocks
    private IqiyiAlbumStandardSPI standardSPI;

    @BeforeEach
    void setUp() {
        // 初始化字段处理器
        standardSPI.initFieldProcessors();
        
        // Mock IqiyiUtil 方法
        when(iqiyiUtil.matchTitle(anyString())).thenAnswer(invocation -> invocation.getArgument(0));
    }

    @Test
    void testIsSpecialMappingField() {
        assertTrue(standardSPI.isSpecialMappingField("year"));
        assertTrue(standardSPI.isSpecialMappingField("actor"));
        assertTrue(standardSPI.isSpecialMappingField("director"));
        assertTrue(standardSPI.isSpecialMappingField("tags"));
        assertTrue(standardSPI.isSpecialMappingField("duration"));
        assertTrue(standardSPI.isSpecialMappingField("featureType"));
        
        assertFalse(standardSPI.isSpecialMappingField("title"));
        assertFalse(standardSPI.isSpecialMappingField("unknown"));
    }

    @Test
    void testProcessYearField() throws NoSuchFieldException {
        StandardAlbum album = new StandardAlbum();
        Field yearField = StandardAlbum.class.getDeclaredField("year");
        
        // 测试正常年份
        standardSPI.processSpecialMappingField(album, "2023年上映", yearField);
        assertEquals(2023, album.getYear());
        
        // 测试包含多个数字的字符串
        album.setYear(null);
        standardSPI.processSpecialMappingField(album, "这是2024年的电影", yearField);
        assertEquals(2024, album.getYear());
        
        // 测试无效输入
        album.setYear(null);
        standardSPI.processSpecialMappingField(album, "无年份信息", yearField);
        assertNull(album.getYear());
        
        // 测试null输入
        album.setYear(null);
        standardSPI.processSpecialMappingField(album, null, yearField);
        assertNull(album.getYear());
    }

    @Test
    void testProcessDurationField() throws NoSuchFieldException {
        StandardAlbum album = new StandardAlbum();
        Field durationField = StandardAlbum.class.getDeclaredField("duration");
        
        // 测试正常时长
        standardSPI.processSpecialMappingField(album, "120", durationField);
        assertEquals(120, album.getDuration());
        
        // 测试无效时长 -1
        standardSPI.processSpecialMappingField(album, "-1", durationField);
        assertEquals(0, album.getDuration());
        
        // 测试无效数字格式
        standardSPI.processSpecialMappingField(album, "abc", durationField);
        assertEquals(0, album.getDuration());
        
        // 测试null输入
        album.setDuration(null);
        standardSPI.processSpecialMappingField(album, null, durationField);
        assertNull(album.getDuration());
    }

    @Test
    void testProcessActorField() throws NoSuchFieldException {
        StandardAlbum album = new StandardAlbum();
        Field actorField = StandardAlbum.class.getDeclaredField("actor");
        
        // 测试正常演员列表
        standardSPI.processSpecialMappingField(album, "张三,李四,王五", actorField);
        assertEquals("张三|李四|王五", album.getActor());
        
        // 测试包含无效值的演员列表
        standardSPI.processSpecialMappingField(album, "张三,其他,李四,未知,王五", actorField);
        assertEquals("张三|李四|王五", album.getActor());
        
        // 测试重复演员
        album.setActor(null);
        standardSPI.processSpecialMappingField(album, "张三,李四,张三,王五", actorField);
        assertEquals("张三|李四|王五", album.getActor());
        
        // 测试空字符串和空白字符
        album.setActor(null);
        standardSPI.processSpecialMappingField(album, "张三, ,李四,  ,王五", actorField);
        assertEquals("张三|李四|王五", album.getActor());
        
        // 测试null输入
        album.setActor(null);
        standardSPI.processSpecialMappingField(album, null, actorField);
        assertNull(album.getActor());
    }

    @Test
    void testProcessDirectorField() throws NoSuchFieldException {
        StandardAlbum album = new StandardAlbum();
        Field directorField = StandardAlbum.class.getDeclaredField("director");
        
        // 测试正常导演列表
        standardSPI.processSpecialMappingField(album, "张艺谋,冯小刚", directorField);
        assertEquals("张艺谋|冯小刚", album.getDirector());
        
        // 测试包含无效值
        standardSPI.processSpecialMappingField(album, "张艺谋,其它,冯小刚", directorField);
        assertEquals("张艺谋|冯小刚", album.getDirector());
    }

    @Test
    void testProcessTagsField() throws NoSuchFieldException {
        StandardAlbum album = new StandardAlbum();
        Field tagsField = StandardAlbum.class.getDeclaredField("tags");
        
        // 测试正常标签列表
        standardSPI.processSpecialMappingField(album, "动作,喜剧,爱情", tagsField);
        assertEquals("动作|喜剧|爱情", album.getTags());
        
        // 测试包含无效值
        standardSPI.processSpecialMappingField(album, "动作,其祂,喜剧,未知", tagsField);
        assertEquals("动作|喜剧", album.getTags());
    }

    @Test
    void testProcessFeatureTypeField() throws NoSuchFieldException {
        StandardAlbum album = new StandardAlbum();
        Field featureTypeField = StandardAlbum.class.getDeclaredField("featureType");

        // 测试映射值 "1" -> 1
        standardSPI.processSpecialMappingField(album, "1", featureTypeField);
        assertEquals(Integer.valueOf(1), album.getFeatureType());

        // 测试映射值 "3" -> 2
        album.setFeatureType(null);
        standardSPI.processSpecialMappingField(album, "3", featureTypeField);
        assertEquals(Integer.valueOf(2), album.getFeatureType());

        // 测试未知值
        album.setFeatureType(null);
        standardSPI.processSpecialMappingField(album, "5", featureTypeField);
        assertNull(album.getFeatureType());

        // 测试null输入
        album.setFeatureType(null);
        standardSPI.processSpecialMappingField(album, null, featureTypeField);
        assertNull(album.getFeatureType());

        // 测试空字符串
        album.setFeatureType(null);
        standardSPI.processSpecialMappingField(album, "", featureTypeField);
        assertNull(album.getFeatureType());

        // 测试空白字符串
        album.setFeatureType(null);
        standardSPI.processSpecialMappingField(album, "  ", featureTypeField);
        assertNull(album.getFeatureType());
    }

    @Test
    void testPostProcessMappingFields() {
        StandardAlbum album = new StandardAlbum();
        album.setTitle("测试标题");
        album.setCategory(1);
        album.setProgramType(2);
        
        standardSPI.postProcessMappingFields(album);
        
        // 验证基础状态字段
        assertEquals(1, album.getStatus());
        assertEquals(0, album.getSourceType());
        assertEquals(1, album.getCopyright());
        assertEquals("iqiyi", album.getCopyrightCode());
        assertEquals(1, album.getProcessStatus());
        assertEquals(0, album.getManagerStatus());
        
        // 验证时间字段不为空
        assertNotNull(album.getCreateTime());
        assertNotNull(album.getUpdateTime());
        
        // 验证标题处理
        assertEquals("测试标题", album.getTitle());
    }

    @Test
    void testGetCpSource() {
        assertEquals("IQIYI_MOBILE", standardSPI.getCpSource().name());
    }

    @Test
    void testEdgeCases() throws NoSuchFieldException {
        StandardAlbum album = new StandardAlbum();
        
        // 测试空字符串输入
        Field actorField = StandardAlbum.class.getDeclaredField("actor");
        standardSPI.processSpecialMappingField(album, "", actorField);
        assertNull(album.getActor());
        
        // 测试只包含分隔符的输入
        album.setActor(null);
        standardSPI.processSpecialMappingField(album, ",,,", actorField);
        assertNull(album.getActor());
        
        // 测试只包含无效值的输入
        album.setActor(null);
        standardSPI.processSpecialMappingField(album, "其他,其它,其祂,未知", actorField);
        assertNull(album.getActor());
    }
}
