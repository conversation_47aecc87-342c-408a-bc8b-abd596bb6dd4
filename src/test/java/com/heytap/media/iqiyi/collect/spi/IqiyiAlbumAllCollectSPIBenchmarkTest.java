package com.heytap.media.iqiyi.collect.spi;

import com.heytap.longvideo.client.media.entity.MisAlbumOrg;
import com.heytap.media.common.thirdparty.spi.collect.CollectContext;
import com.heytap.media.iqiyi.config.CollectConfig;
import com.heytap.media.iqiyi.constant.IqiyiConstant;
import com.heytap.media.iqiyi.util.IqiyiMediaCollectPageHandler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * IqiyiAlbumAllCollectSPI 性能基准测试
 * 测试 Java 8 优化后的性能表现
 */
@ExtendWith(MockitoExtension.class)
class IqiyiAlbumAllCollectSPIBenchmarkTest {

    @Mock
    private IqiyiMediaCollectPageHandler iqiyiMediaCollectPageHandler;

    @Mock
    private CollectConfig collectConfig;

    @InjectMocks
    private IqiyiAlbumAllCollectSPI collectSPI;

    private List<MisAlbumOrg> testDataSet;
    private static final int TEST_DATA_SIZE = 10000;

    @BeforeEach
    void setUp() {
        when(collectConfig.getApiKey()).thenReturn("test-api-key");
        when(collectConfig.getAlbumPageSize()).thenReturn("20");
        
        // 准备测试数据集
        prepareTestDataSet();
    }

    @Test
    void benchmarkFilterPerformance() {
        System.out.println("=== 过滤性能基准测试 ===");
        
        // 预热 JVM
        warmUp();
        
        // 单线程性能测试
        long singleThreadTime = benchmarkSingleThreadFiltering();
        System.out.println("单线程处理 " + TEST_DATA_SIZE + " 条数据耗时: " + singleThreadTime + "ms");
        System.out.println("单线程平均每条数据处理时间: " + (double)singleThreadTime / TEST_DATA_SIZE + "ms");
        
        // 并行流性能测试
        long parallelStreamTime = benchmarkParallelStreamFiltering();
        System.out.println("并行流处理 " + TEST_DATA_SIZE + " 条数据耗时: " + parallelStreamTime + "ms");
        System.out.println("并行流平均每条数据处理时间: " + (double)parallelStreamTime / TEST_DATA_SIZE + "ms");
        
        // 性能提升比较
        double speedup = (double)singleThreadTime / parallelStreamTime;
        System.out.println("并行处理性能提升: " + String.format("%.2f", speedup) + "x");
        
        // 性能断言
        assertTrue(singleThreadTime < 10000, "单线程处理时间应该在10秒内");
        assertTrue(parallelStreamTime < singleThreadTime, "并行处理应该更快");
    }

    @Test
    void benchmarkGetParamsPerformance() {
        System.out.println("=== 参数构建性能基准测试 ===");
        
        CollectContext context = createTestContext();
        
        // 预热
        IntStream.range(0, 1000).forEach(i -> collectSPI.getParams(context));
        
        long startTime = System.nanoTime();
        
        // 执行测试
        IntStream.range(0, 100000)
                .forEach(i -> collectSPI.getParams(context));
        
        long endTime = System.nanoTime();
        long duration = TimeUnit.NANOSECONDS.toMillis(endTime - startTime);
        
        System.out.println("构建100000次参数耗时: " + duration + "ms");
        System.out.println("平均每次构建时间: " + (double)duration / 100000 + "ms");
        
        assertTrue(duration < 1000, "参数构建性能应该在1秒内完成100000次");
    }

    @Test
    void benchmarkMemoryUsage() {
        System.out.println("=== 内存使用基准测试 ===");
        
        Runtime runtime = Runtime.getRuntime();
        
        // 强制垃圾回收
        System.gc();
        long beforeMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 处理大量数据
        List<Boolean> results = new ArrayList<>();
        for (MisAlbumOrg mediaOrg : testDataSet) {
            results.add(collectSPI.filter(mediaOrg));
        }
        
        long afterMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryUsed = afterMemory - beforeMemory;
        
        System.out.println("处理 " + TEST_DATA_SIZE + " 条数据使用内存: " + memoryUsed / 1024 / 1024 + "MB");
        System.out.println("平均每条数据内存使用: " + memoryUsed / TEST_DATA_SIZE + " bytes");
        
        // 验证结果正确性
        assertEquals(TEST_DATA_SIZE, results.size());
        
        // 内存使用应该合理
        assertTrue(memoryUsed < 100 * 1024 * 1024, "内存使用应该在100MB以内");
    }

    @Test
    void benchmarkOptionalVsTraditional() {
        System.out.println("=== Optional vs 传统null检查性能对比 ===");
        
        CollectContext context = createTestContext();
        
        // 预热
        IntStream.range(0, 1000).forEach(i -> collectSPI.getParams(context));
        
        // 测试 Optional 风格的参数构建
        long optionalStartTime = System.nanoTime();
        IntStream.range(0, 50000)
                .forEach(i -> collectSPI.getParams(context));
        long optionalEndTime = System.nanoTime();
        long optionalDuration = TimeUnit.NANOSECONDS.toMillis(optionalEndTime - optionalStartTime);
        
        System.out.println("Optional 风格处理50000次耗时: " + optionalDuration + "ms");
        
        // 这里可以添加传统null检查的对比实现
        // 由于我们已经优化为Optional风格，这里主要验证性能可接受
        assertTrue(optionalDuration < 2000, "Optional 风格性能应该可接受");
    }

    @Test
    void benchmarkStreamVsLoop() {
        System.out.println("=== Stream vs 传统循环性能对比 ===");
        
        // 创建测试数据
        List<String> testFields = IntStream.range(0, 1000)
                .mapToObj(i -> "field" + i)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
        
        // Stream 方式
        long streamStartTime = System.nanoTime();
        IntStream.range(0, 10000).forEach(i -> {
            boolean hasEmpty = testFields.stream().anyMatch(String::isEmpty);
        });
        long streamEndTime = System.nanoTime();
        long streamDuration = TimeUnit.NANOSECONDS.toMillis(streamEndTime - streamStartTime);
        
        // 传统循环方式
        long loopStartTime = System.nanoTime();
        IntStream.range(0, 10000).forEach(i -> {
            boolean hasEmpty = false;
            for (String field : testFields) {
                if (field.isEmpty()) {
                    hasEmpty = true;
                    break;
                }
            }
        });
        long loopEndTime = System.nanoTime();
        long loopDuration = TimeUnit.NANOSECONDS.toMillis(loopEndTime - loopStartTime);
        
        System.out.println("Stream 方式耗时: " + streamDuration + "ms");
        System.out.println("传统循环耗时: " + loopDuration + "ms");
        
        // 两种方式性能都应该可接受
        assertTrue(streamDuration < 5000, "Stream 性能应该可接受");
        assertTrue(loopDuration < 5000, "循环性能应该可接受");
    }

    @Test
    void benchmarkConcurrentAccess() {
        System.out.println("=== 并发访问性能测试 ===");
        
        long startTime = System.currentTimeMillis();
        
        // 并发执行过滤操作
        testDataSet.parallelStream()
                .forEach(collectSPI::filter);
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println("并发处理 " + TEST_DATA_SIZE + " 条数据耗时: " + duration + "ms");
        System.out.println("并发平均每条数据处理时间: " + (double)duration / TEST_DATA_SIZE + "ms");
        
        assertTrue(duration < 5000, "并发处理性能应该在5秒内");
    }

    // 辅助方法
    private void prepareTestDataSet() {
        testDataSet = new ArrayList<>();
        
        // 创建不同类型的测试数据
        IntStream.range(0, TEST_DATA_SIZE).forEach(i -> {
            MisAlbumOrg mediaOrg = new MisAlbumOrg();
            
            if (i % 10 == 0) {
                // 10% 无效数据
                mediaOrg.setOriginInfo("");
            } else if (i % 5 == 0) {
                // 20% UGC 数据
                mediaOrg.setOriginInfo(createUgcJson());
            } else if (i % 3 == 0) {
                // 33% 短视频数据
                mediaOrg.setOriginInfo(createShortVideoJson());
            } else {
                // 其余为有效数据
                mediaOrg.setOriginInfo(createValidJson());
            }
            
            testDataSet.add(mediaOrg);
        });
    }

    private void warmUp() {
        // JVM 预热
        IntStream.range(0, 1000)
                .forEach(i -> {
                    MisAlbumOrg mediaOrg = testDataSet.get(i % testDataSet.size());
                    collectSPI.filter(mediaOrg);
                });
    }

    private long benchmarkSingleThreadFiltering() {
        long startTime = System.currentTimeMillis();
        
        for (MisAlbumOrg mediaOrg : testDataSet) {
            collectSPI.filter(mediaOrg);
        }
        
        return System.currentTimeMillis() - startTime;
    }

    private long benchmarkParallelStreamFiltering() {
        long startTime = System.currentTimeMillis();
        
        testDataSet.parallelStream()
                .forEach(collectSPI::filter);
        
        return System.currentTimeMillis() - startTime;
    }

    private CollectContext createTestContext() {
        CollectContext context = new CollectContext();
        context.setPageNo(2);
        
        Map<String, Object> bizMap = new HashMap<>();
        bizMap.put(IqiyiConstant.CATEGORY_ID, "1");
        bizMap.put("minId", 12345);
        context.setBizMap(bizMap);
        
        return context;
    }

    private String createValidJson() {
        return "{\"name\":\"测试\",\"h5Url\":\"http://test.com\",\"imageUrl\":\"http://img.com\",\"channelId\":2,\"contentType\":1,\"rugc\":0,\"pugc\":0,\"playControls\":[{\"platformId\":10,\"availableStatus\":1}]}";
    }

    private String createUgcJson() {
        return "{\"name\":\"测试\",\"h5Url\":\"http://test.com\",\"imageUrl\":\"http://img.com\",\"channelId\":2,\"contentType\":1,\"rugc\":1,\"pugc\":0,\"playControls\":[{\"platformId\":10,\"availableStatus\":1}]}";
    }

    private String createShortVideoJson() {
        return "{\"name\":\"测试\",\"h5Url\":\"http://test.com\",\"imageUrl\":\"http://img.com\",\"channelId\":2,\"contentType\":1,\"rugc\":0,\"pugc\":0,\"id\":\"123\",\"featureAlbumId\":\"123\",\"playControls\":[{\"platformId\":10,\"availableStatus\":1}]}";
    }
}
