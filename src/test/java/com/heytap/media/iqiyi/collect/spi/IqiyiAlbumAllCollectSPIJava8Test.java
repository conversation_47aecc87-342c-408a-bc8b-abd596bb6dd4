package com.heytap.media.iqiyi.collect.spi;

import com.heytap.longvideo.client.media.entity.MisAlbumOrg;
import com.heytap.media.common.thirdparty.spi.collect.CollectContext;
import com.heytap.media.iqiyi.config.CollectConfig;
import com.heytap.media.iqiyi.constant.IqiyiConstant;
import com.heytap.media.iqiyi.util.IqiyiMediaCollectPageHandler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * IqiyiAlbumAllCollectSPI Java 8 特性测试类
 * 验证 Java 8 兼容性、函数式编程和性能
 */
@ExtendWith(MockitoExtension.class)
class IqiyiAlbumAllCollectSPIJava8Test {

    @Mock
    private IqiyiMediaCollectPageHandler iqiyiMediaCollectPageHandler;

    @Mock
    private CollectConfig collectConfig;

    @InjectMocks
    private IqiyiAlbumAllCollectSPI collectSPI;

    @BeforeEach
    void setUp() {
        when(collectConfig.getApiKey()).thenReturn("test-api-key");
        when(collectConfig.getAlbumPageSize()).thenReturn("20");
    }

    @Test
    void testGetParamsWithOptionalChaining() {
        // 测试 Optional 链式调用的参数处理
        CollectContext context = createMockContext(1, "2");
        
        Map<String, String> params = collectSPI.getParams(context);
        
        // 验证基础参数
        assertEquals("test-api-key", params.get("apiKey"));
        assertEquals("20", params.get("pageSize"));
        assertEquals("1", params.get("status"));
        assertEquals("2", params.get(IqiyiConstant.CATEGORY_ID));
        assertNotNull(params.get("ts"));
        
        // 第一页不应该有 minId
        assertNull(params.get("minId"));
    }

    @Test
    void testGetParamsWithMovieChannel() {
        // 测试电影频道的特殊处理
        CollectContext context = createMockContext(1, "1");
        
        Map<String, String> params = collectSPI.getParams(context);
        
        assertEquals("1", params.get("solo"));
        assertEquals("1", params.get(IqiyiConstant.CATEGORY_ID));
    }

    @Test
    void testGetParamsWithPagination() {
        // 测试分页参数处理
        CollectContext context = createMockContext(2, "3");
        context.getBizMap().put("minId", 12345);
        
        Map<String, String> params = collectSPI.getParams(context);
        
        assertEquals("12345", params.get("minId"));
    }

    @Test
    void testFilterWithValidData() {
        // 测试有效数据不被过滤
        MisAlbumOrg mediaOrg = createValidMediaOrg();
        
        boolean shouldFilter = collectSPI.filter(mediaOrg);
        
        assertFalse(shouldFilter, "有效数据不应该被过滤");
    }

    @Test
    void testFilterWithInvalidInput() {
        // 测试无效输入的处理
        assertAll("无效输入测试",
            () -> assertTrue(collectSPI.filter(null), "null 输入应该被过滤"),
            () -> assertTrue(collectSPI.filter(new MisAlbumOrg()), "空 originInfo 应该被过滤"),
            () -> {
                MisAlbumOrg emptyOrg = new MisAlbumOrg();
                emptyOrg.setOriginInfo("");
                assertTrue(collectSPI.filter(emptyOrg), "空字符串 originInfo 应该被过滤");
            }
        );
    }

    @Test
    void testFilterWithInvalidJson() {
        // 测试无效 JSON 的处理
        MisAlbumOrg mediaOrg = new MisAlbumOrg();
        mediaOrg.setOriginInfo("invalid json string");
        
        boolean shouldFilter = collectSPI.filter(mediaOrg);
        
        assertTrue(shouldFilter, "无效 JSON 应该被过滤");
    }

    @Test
    void testFilterWithMissingRequiredFields() {
        // 测试缺少必需字段的情况
        MisAlbumOrg mediaOrg = new MisAlbumOrg();
        mediaOrg.setOriginInfo(createJsonWithMissingFields());
        
        boolean shouldFilter = collectSPI.filter(mediaOrg);
        
        assertTrue(shouldFilter, "缺少必需字段应该被过滤");
    }

    @Test
    void testFilterWithUgcContent() {
        // 测试 UGC 内容过滤
        MisAlbumOrg mediaOrg = new MisAlbumOrg();
        mediaOrg.setOriginInfo(createUgcContentJson());
        
        boolean shouldFilter = collectSPI.filter(mediaOrg);
        
        assertTrue(shouldFilter, "UGC 内容应该被过滤");
    }

    @Test
    void testFilterWithInvalidContentType() {
        // 测试无效内容类型过滤
        MisAlbumOrg mediaOrg = new MisAlbumOrg();
        mediaOrg.setOriginInfo(createInvalidContentTypeJson());
        
        boolean shouldFilter = collectSPI.filter(mediaOrg);
        
        assertTrue(shouldFilter, "无效内容类型应该被过滤");
    }

    @Test
    void testFilterWithShortVideo() {
        // 测试短视频过滤
        MisAlbumOrg mediaOrg = new MisAlbumOrg();
        mediaOrg.setOriginInfo(createShortVideoJson());
        
        boolean shouldFilter = collectSPI.filter(mediaOrg);
        
        assertTrue(shouldFilter, "短视频应该被过滤");
    }

    @Test
    void testFilterWithMovieChannelShortVideo() {
        // 测试电影频道短视频过滤
        MisAlbumOrg mediaOrg = new MisAlbumOrg();
        mediaOrg.setOriginInfo(createMovieChannelShortVideoJson());
        
        boolean shouldFilter = collectSPI.filter(mediaOrg);
        
        assertTrue(shouldFilter, "电影频道短视频应该被过滤");
    }

    @Test
    void testOptionalChaining() {
        // 测试 Optional 链式调用的安全性
        CollectContext context = new CollectContext();
        context.setBizMap(new HashMap<>());
        
        // 应该不抛出异常
        assertDoesNotThrow(() -> {
            Map<String, String> params = collectSPI.getParams(context);
            assertNotNull(params);
        });
    }

    @Test
    void testStreamProcessingPerformance() {
        // 测试 Stream 处理的性能
        long startTime = System.currentTimeMillis();
        
        // 并行处理多个过滤请求
        IntStream.range(0, 1000)
                .parallel()
                .mapToObj(i -> createValidMediaOrg())
                .forEach(collectSPI::filter);
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println("处理1000个过滤请求耗时: " + duration + "ms");
        assertTrue(duration < 5000, "性能测试：处理时间应该在5秒内");
    }

    @Test
    void testConcurrentFiltering() {
        // 测试并发过滤的安全性
        MisAlbumOrg mediaOrg = createValidMediaOrg();
        
        // 使用 CompletableFuture 进行并发测试
        CompletableFuture<Boolean>[] futures = IntStream.range(0, 100)
                .mapToObj(i -> CompletableFuture.supplyAsync(() -> collectSPI.filter(mediaOrg)))
                .toArray(CompletableFuture[]::new);
        
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures);
        
        assertDoesNotThrow(() -> {
            allOf.get();
            // 验证所有结果一致
            boolean firstResult = futures[0].get();
            for (CompletableFuture<Boolean> future : futures) {
                assertEquals(firstResult, future.get(), "并发结果应该一致");
            }
        });
    }

    @Test
    void testFunctionalProgrammingStyle() {
        // 测试函数式编程风格的正确性
        MisAlbumOrg validOrg = createValidMediaOrg();
        MisAlbumOrg invalidOrg = new MisAlbumOrg();
        
        // 使用 Optional 进行安全处理
        Optional<Boolean> validResult = Optional.ofNullable(validOrg)
                .map(collectSPI::filter);
        
        Optional<Boolean> invalidResult = Optional.ofNullable(invalidOrg)
                .map(collectSPI::filter);
        
        assertTrue(validResult.isPresent());
        assertTrue(invalidResult.isPresent());
        assertFalse(validResult.get());
        assertTrue(invalidResult.get());
    }

    // 辅助方法
    private CollectContext createMockContext(int pageNo, String categoryId) {
        CollectContext context = new CollectContext();
        context.setPageNo(pageNo);
        
        Map<String, Object> bizMap = new HashMap<>();
        bizMap.put(IqiyiConstant.CATEGORY_ID, categoryId);
        context.setBizMap(bizMap);
        
        return context;
    }

    private MisAlbumOrg createValidMediaOrg() {
        MisAlbumOrg mediaOrg = new MisAlbumOrg();
        mediaOrg.setOriginInfo(createValidJson());
        return mediaOrg;
    }

    private String createValidJson() {
        return "{\n" +
                "  \"name\": \"测试剧集\",\n" +
                "  \"h5Url\": \"https://example.com/test\",\n" +
                "  \"imageUrl\": \"https://example.com/image.jpg\",\n" +
                "  \"channelId\": 2,\n" +
                "  \"contentType\": 1,\n" +
                "  \"rugc\": 0,\n" +
                "  \"pugc\": 0,\n" +
                "  \"duration\": 3600,\n" +
                "  \"playControls\": [\n" +
                "    {\n" +
                "      \"platformId\": 10,\n" +
                "      \"availableStatus\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}";
    }

    private String createJsonWithMissingFields() {
        return "{\n" +
                "  \"channelId\": 2,\n" +
                "  \"contentType\": 1\n" +
                "}";
    }

    private String createUgcContentJson() {
        return "{\n" +
                "  \"name\": \"测试剧集\",\n" +
                "  \"h5Url\": \"https://example.com/test\",\n" +
                "  \"imageUrl\": \"https://example.com/image.jpg\",\n" +
                "  \"channelId\": 2,\n" +
                "  \"contentType\": 1,\n" +
                "  \"rugc\": 1,\n" +
                "  \"pugc\": 0,\n" +
                "  \"playControls\": [{\"platformId\": 10, \"availableStatus\": 1}]\n" +
                "}";
    }

    private String createInvalidContentTypeJson() {
        return "{\n" +
                "  \"name\": \"测试剧集\",\n" +
                "  \"h5Url\": \"https://example.com/test\",\n" +
                "  \"imageUrl\": \"https://example.com/image.jpg\",\n" +
                "  \"channelId\": 2,\n" +
                "  \"contentType\": 5,\n" +
                "  \"rugc\": 0,\n" +
                "  \"pugc\": 0,\n" +
                "  \"playControls\": [{\"platformId\": 10, \"availableStatus\": 1}]\n" +
                "}";
    }

    private String createShortVideoJson() {
        return "{\n" +
                "  \"name\": \"测试剧集\",\n" +
                "  \"h5Url\": \"https://example.com/test\",\n" +
                "  \"imageUrl\": \"https://example.com/image.jpg\",\n" +
                "  \"channelId\": 2,\n" +
                "  \"contentType\": 1,\n" +
                "  \"rugc\": 0,\n" +
                "  \"pugc\": 0,\n" +
                "  \"id\": \"123\",\n" +
                "  \"featureAlbumId\": \"123\",\n" +
                "  \"playControls\": [{\"platformId\": 10, \"availableStatus\": 1}]\n" +
                "}";
    }

    private String createMovieChannelShortVideoJson() {
        return "{\n" +
                "  \"name\": \"测试剧集\",\n" +
                "  \"h5Url\": \"https://example.com/test\",\n" +
                "  \"imageUrl\": \"https://example.com/image.jpg\",\n" +
                "  \"channelId\": 1,\n" +
                "  \"contentType\": 1,\n" +
                "  \"rugc\": 0,\n" +
                "  \"pugc\": 0,\n" +
                "  \"duration\": 300,\n" +
                "  \"playControls\": [{\"platformId\": 10, \"availableStatus\": 1}]\n" +
                "}";
    }
}
