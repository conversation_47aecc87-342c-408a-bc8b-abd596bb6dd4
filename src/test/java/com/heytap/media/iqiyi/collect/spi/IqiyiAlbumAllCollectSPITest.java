package com.heytap.media.iqiyi.collect.spi;

import com.heytap.longvideo.client.media.entity.MisAlbumOrg;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * IqiyiAlbumAllCollectSPI 测试类
 * 用于验证圈复杂度优化后的逻辑正确性
 */
@ExtendWith(MockitoExtension.class)
class IqiyiAlbumAllCollectSPITest {

    @InjectMocks
    private IqiyiAlbumAllCollectSPI iqiyiAlbumAllCollectSPI;

    private MisAlbumOrg validMediaOrg;
    private MisAlbumOrg invalidMediaOrg;

    @BeforeEach
    void setUp() {
        // 创建有效的测试数据
        validMediaOrg = new MisAlbumOrg();
        validMediaOrg.setOriginInfo(createValidJsonString());

        // 创建无效的测试数据
        invalidMediaOrg = new MisAlbumOrg();
        invalidMediaOrg.setOriginInfo("");
    }

    @Test
    void testFilter_NullMediaOrg_ShouldReturnTrue() {
        assertTrue(iqiyiAlbumAllCollectSPI.filter(null));
    }

    @Test
    void testFilter_EmptyOriginInfo_ShouldReturnTrue() {
        assertTrue(iqiyiAlbumAllCollectSPI.filter(invalidMediaOrg));
    }

    @Test
    void testFilter_InvalidJson_ShouldReturnTrue() {
        MisAlbumOrg mediaOrg = new MisAlbumOrg();
        mediaOrg.setOriginInfo("invalid json");
        assertTrue(iqiyiAlbumAllCollectSPI.filter(mediaOrg));
    }

    @Test
    void testFilter_ValidData_ShouldReturnFalse() {
        assertFalse(iqiyiAlbumAllCollectSPI.filter(validMediaOrg));
    }

    @Test
    void testFilter_MissingPlayControls_ShouldReturnFalse() {
        MisAlbumOrg mediaOrg = new MisAlbumOrg();
        mediaOrg.setOriginInfo(createJsonWithoutPlayControls());
        // 应该不过滤，因为没有播控信息时不过滤
        assertFalse(iqiyiAlbumAllCollectSPI.filter(mediaOrg));
    }

    @Test
    void testFilter_EmptyPlayControls_ShouldReturnTrue() {
        MisAlbumOrg mediaOrg = new MisAlbumOrg();
        mediaOrg.setOriginInfo(createJsonWithEmptyPlayControls());
        assertTrue(iqiyiAlbumAllCollectSPI.filter(mediaOrg));
    }

    @Test
    void testFilter_InvalidPlayControls_ShouldReturnTrue() {
        MisAlbumOrg mediaOrg = new MisAlbumOrg();
        mediaOrg.setOriginInfo(createJsonWithInvalidPlayControls());
        assertTrue(iqiyiAlbumAllCollectSPI.filter(mediaOrg));
    }

    @Test
    void testFilter_UgcContent_ShouldReturnTrue() {
        MisAlbumOrg mediaOrg = new MisAlbumOrg();
        mediaOrg.setOriginInfo(createJsonWithUgcContent());
        assertTrue(iqiyiAlbumAllCollectSPI.filter(mediaOrg));
    }

    @Test
    void testFilter_InvalidContentType_ShouldReturnTrue() {
        MisAlbumOrg mediaOrg = new MisAlbumOrg();
        mediaOrg.setOriginInfo(createJsonWithInvalidContentType());
        assertTrue(iqiyiAlbumAllCollectSPI.filter(mediaOrg));
    }

    @Test
    void testFilter_MissingRequiredFields_ShouldReturnTrue() {
        MisAlbumOrg mediaOrg = new MisAlbumOrg();
        mediaOrg.setOriginInfo(createJsonWithMissingFields());
        assertTrue(iqiyiAlbumAllCollectSPI.filter(mediaOrg));
    }

    @Test
    void testFilter_ShortVideo_ShouldReturnTrue() {
        MisAlbumOrg mediaOrg = new MisAlbumOrg();
        mediaOrg.setOriginInfo(createJsonWithShortVideo());
        assertTrue(iqiyiAlbumAllCollectSPI.filter(mediaOrg));
    }

    @Test
    void testFilter_MovieChannelShortVideo_ShouldReturnTrue() {
        MisAlbumOrg mediaOrg = new MisAlbumOrg();
        mediaOrg.setOriginInfo(createJsonWithMovieChannelShortVideo());
        assertTrue(iqiyiAlbumAllCollectSPI.filter(mediaOrg));
    }

    // 辅助方法创建测试JSON数据
    private String createValidJsonString() {
        return "{\n" +
                "  \"name\": \"测试剧集\",\n" +
                "  \"h5Url\": \"https://example.com/test\",\n" +
                "  \"imageUrl\": \"https://example.com/image.jpg\",\n" +
                "  \"channelId\": 2,\n" +
                "  \"contentType\": 1,\n" +
                "  \"rugc\": 0,\n" +
                "  \"pugc\": 0,\n" +
                "  \"duration\": 3600,\n" +
                "  \"playControls\": [\n" +
                "    {\n" +
                "      \"platformId\": 10,\n" +
                "      \"availableStatus\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}";
    }

    private String createJsonWithoutPlayControls() {
        return "{\n" +
                "  \"name\": \"测试剧集\",\n" +
                "  \"h5Url\": \"https://example.com/test\",\n" +
                "  \"imageUrl\": \"https://example.com/image.jpg\",\n" +
                "  \"channelId\": 2,\n" +
                "  \"contentType\": 1,\n" +
                "  \"rugc\": 0,\n" +
                "  \"pugc\": 0,\n" +
                "  \"duration\": 3600\n" +
                "}";
    }

    private String createJsonWithEmptyPlayControls() {
        return "{\n" +
                "  \"name\": \"测试剧集\",\n" +
                "  \"h5Url\": \"https://example.com/test\",\n" +
                "  \"imageUrl\": \"https://example.com/image.jpg\",\n" +
                "  \"channelId\": 2,\n" +
                "  \"contentType\": 1,\n" +
                "  \"rugc\": 0,\n" +
                "  \"pugc\": 0,\n" +
                "  \"duration\": 3600,\n" +
                "  \"playControls\": []\n" +
                "}";
    }

    private String createJsonWithInvalidPlayControls() {
        return "{\n" +
                "  \"name\": \"测试剧集\",\n" +
                "  \"h5Url\": \"https://example.com/test\",\n" +
                "  \"imageUrl\": \"https://example.com/image.jpg\",\n" +
                "  \"channelId\": 2,\n" +
                "  \"contentType\": 1,\n" +
                "  \"rugc\": 0,\n" +
                "  \"pugc\": 0,\n" +
                "  \"duration\": 3600,\n" +
                "  \"playControls\": [\n" +
                "    {\n" +
                "      \"platformId\": 5,\n" +
                "      \"availableStatus\": 0\n" +
                "    }\n" +
                "  ]\n" +
                "}";
    }

    private String createJsonWithUgcContent() {
        return "{\n" +
                "  \"name\": \"测试剧集\",\n" +
                "  \"h5Url\": \"https://example.com/test\",\n" +
                "  \"imageUrl\": \"https://example.com/image.jpg\",\n" +
                "  \"channelId\": 2,\n" +
                "  \"contentType\": 1,\n" +
                "  \"rugc\": 1,\n" +
                "  \"pugc\": 0,\n" +
                "  \"duration\": 3600,\n" +
                "  \"playControls\": [\n" +
                "    {\n" +
                "      \"platformId\": 10,\n" +
                "      \"availableStatus\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}";
    }

    private String createJsonWithInvalidContentType() {
        return "{\n" +
                "  \"name\": \"测试剧集\",\n" +
                "  \"h5Url\": \"https://example.com/test\",\n" +
                "  \"imageUrl\": \"https://example.com/image.jpg\",\n" +
                "  \"channelId\": 2,\n" +
                "  \"contentType\": 2,\n" +
                "  \"rugc\": 0,\n" +
                "  \"pugc\": 0,\n" +
                "  \"duration\": 3600,\n" +
                "  \"playControls\": [\n" +
                "    {\n" +
                "      \"platformId\": 10,\n" +
                "      \"availableStatus\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}";
    }

    private String createJsonWithMissingFields() {
        return "{\n" +
                "  \"channelId\": 2,\n" +
                "  \"contentType\": 1,\n" +
                "  \"rugc\": 0,\n" +
                "  \"pugc\": 0,\n" +
                "  \"duration\": 3600,\n" +
                "  \"playControls\": [\n" +
                "    {\n" +
                "      \"platformId\": 10,\n" +
                "      \"availableStatus\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}";
    }

    private String createJsonWithShortVideo() {
        return "{\n" +
                "  \"name\": \"测试剧集\",\n" +
                "  \"h5Url\": \"https://example.com/test\",\n" +
                "  \"imageUrl\": \"https://example.com/image.jpg\",\n" +
                "  \"channelId\": 2,\n" +
                "  \"contentType\": 1,\n" +
                "  \"rugc\": 0,\n" +
                "  \"pugc\": 0,\n" +
                "  \"duration\": 3600,\n" +
                "  \"id\": \"123\",\n" +
                "  \"featureAlbumId\": \"123\",\n" +
                "  \"playControls\": [\n" +
                "    {\n" +
                "      \"platformId\": 10,\n" +
                "      \"availableStatus\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}";
    }

    private String createJsonWithMovieChannelShortVideo() {
        return "{\n" +
                "  \"name\": \"测试剧集\",\n" +
                "  \"h5Url\": \"https://example.com/test\",\n" +
                "  \"imageUrl\": \"https://example.com/image.jpg\",\n" +
                "  \"channelId\": 1,\n" +
                "  \"contentType\": 1,\n" +
                "  \"rugc\": 0,\n" +
                "  \"pugc\": 0,\n" +
                "  \"duration\": 300,\n" +
                "  \"playControls\": [\n" +
                "    {\n" +
                "      \"platformId\": 10,\n" +
                "      \"availableStatus\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}";
    }
}
