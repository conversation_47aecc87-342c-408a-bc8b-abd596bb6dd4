<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.heytap.media</groupId>
    <artifactId>media-iqiyi-plugin</artifactId>
    <version>1.0.1</version>
    <name>media-iqiyi-plugin</name>
    <description>media-iqiyi-plugin</description>

    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <!-- maven私服配置 -->
    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>nexus-releases</name>
            <url>http://nexus.os.adc.com/nexus/content/repositories/releases/</url>
        </repository>

        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>nexus-snapshots</name>
            <url>http://nexus.os.adc.com/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <dependencies>
        <dependency>
            <groupId>com.heytap.media</groupId>
            <artifactId>media-common-lib</artifactId>
            <version>1.9.1</version>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.cal10n</groupId>
                    <artifactId>cal10n-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.heytap.longvideo</groupId>
            <artifactId>longvideo-media-client-lib</artifactId>
            <version>1.0.47</version>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.2.0</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <!-- 源码插件 -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <!-- 发布时自动将源码发布到私服 -->
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>